#!/usr/bin/env python3
"""
Advanced Response Guardrails for Medical Chatbot
Validates and enhances LLM responses for safety and compliance
"""

import re
import json
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
from datetime import datetime

# Advanced libraries for response validation
try:
    from detoxify import Detoxify
    DETOXIFY_AVAILABLE = True
except ImportError:
    DETOXIFY_AVAILABLE = False

try:
    import spacy
    SPACY_AVAILABLE = True
except ImportError:
    SPACY_AVAILABLE = False
    print("⚠️ spaCy not available. Install with: pip install spacy && python -m spacy download en_core_web_sm")

@dataclass
class ResponseGuardResult:
    """Result of response guardrail analysis"""
    is_compliant: bool
    risk_level: str  # "low", "medium", "high", "critical"
    detected_issues: List[str]
    medical_compliance: bool
    toxicity_score: float
    inappropriate_advice: bool
    enhanced_response: str
    disclaimers_added: List[str]
    confidence_score: float
    action_taken: str

class AdvancedResponseGuard:
    """Advanced response validation and enhancement"""
    
    def __init__(self):
        self.detoxify_model = None
        self.nlp = None
        
        # Initialize Detoxify for response toxicity checking
        if DETOXIFY_AVAILABLE:
            try:
                self.detoxify_model = Detoxify('original')
                print("✅ Response toxicity detection initialized")
            except Exception as e:
                print(f"⚠️ Response Detoxify initialization failed: {e}")
        
        # Initialize spaCy for medical entity recognition
        if SPACY_AVAILABLE:
            try:
                self.nlp = spacy.load("en_core_web_sm")
                print("✅ spaCy medical entity recognition initialized")
            except Exception as e:
                print(f"⚠️ spaCy initialization failed: {e}")
        
        # Prohibited medical advice patterns
        self.prohibited_advice_patterns = [
            r'\b(?:i can diagnose|you definitely have|this will cure)\b',
            r'\b(?:stop taking your medication|increase your dose)\b',
            r'\b(?:you don\'t need a doctor|skip the appointment)\b',
            r'\b(?:this is medical advice|i recommend treatment)\b',
            r'\b(?:take this medication|dosage should be)\b'
        ]
        
        # Medical disclaimer templates
        self.medical_disclaimers = {
            "general": "\n\n⚠️ **Medical Disclaimer**: This information is for educational purposes only and should not replace professional medical advice. Please consult with a qualified healthcare provider for proper diagnosis and treatment.",
            "emergency": "\n\n🚨 **Emergency Notice**: If you're experiencing a medical emergency, please call emergency services immediately (911 in US, 999 in UK, 112 in EU).",
            "medication": "\n\n💊 **Medication Notice**: Never start, stop, or change medications without consulting your healthcare provider. This information is not a substitute for professional medical advice.",
            "diagnosis": "\n\n🩺 **Diagnosis Notice**: Only qualified healthcare professionals can provide medical diagnoses. This information is for educational purposes only."
        }
        
        # Medical compliance keywords
        self.medical_keywords = [
            'symptom', 'diagnosis', 'treatment', 'medication', 'prescription',
            'doctor', 'hospital', 'clinic', 'therapy', 'surgery', 'condition',
            'disease', 'illness', 'pain', 'fever', 'infection', 'allergy'
        ]
        
        # Empathy enhancement patterns
        self.empathy_enhancers = [
            "I understand this must be concerning for you.",
            "I can imagine how you're feeling about this.",
            "This sounds like a difficult situation.",
            "I appreciate you sharing this with me.",
            "Your health concerns are important."
        ]
    
    def validate_response(self, response: str, user_context: Dict[str, Any] = None) -> ResponseGuardResult:
        """Comprehensive response validation and enhancement"""
        
        # Initialize result
        result = ResponseGuardResult(
            is_compliant=True,
            risk_level="low",
            detected_issues=[],
            medical_compliance=True,
            toxicity_score=0.0,
            inappropriate_advice=False,
            enhanced_response=response,
            disclaimers_added=[],
            confidence_score=0.0,
            action_taken="approved"
        )
        
        # 1. Check for prohibited medical advice
        prohibited_advice = self._check_prohibited_advice(response)
        if prohibited_advice:
            result.inappropriate_advice = True
            result.is_compliant = False
            result.risk_level = "high"
            result.detected_issues.append("prohibited_medical_advice")
            result.action_taken = "blocked"
            return result
        
        # 2. Toxicity check using Detoxify
        toxicity_score = self._check_response_toxicity(response)
        result.toxicity_score = toxicity_score
        if toxicity_score > 0.5:
            result.is_compliant = False
            result.risk_level = "high"
            result.detected_issues.append("toxic_response")
            result.action_taken = "blocked"
            return result
        
        # 3. Medical compliance check
        medical_compliance = self._check_medical_compliance(response)
        result.medical_compliance = medical_compliance
        if not medical_compliance:
            result.detected_issues.append("medical_non_compliance")
            result.risk_level = "medium"
        
        # 4. Enhance response with appropriate disclaimers
        enhanced_response = self._enhance_with_disclaimers(response, user_context)
        result.enhanced_response = enhanced_response
        
        # 5. Add empathy if needed
        if user_context and user_context.get('preferred_style') == 'empathetic':
            result.enhanced_response = self._add_empathy(result.enhanced_response)
        
        # 6. Calculate confidence score
        result.confidence_score = self._calculate_confidence_score(result)
        
        # 7. Final compliance check
        if result.is_compliant and len(result.detected_issues) == 0:
            result.action_taken = "approved"
        elif len(result.detected_issues) > 0 and result.is_compliant:
            result.action_taken = "enhanced"
        
        return result
    
    def _check_prohibited_advice(self, response: str) -> bool:
        """Check for prohibited medical advice patterns"""
        response_lower = response.lower()
        for pattern in self.prohibited_advice_patterns:
            if re.search(pattern, response_lower, re.IGNORECASE):
                return True
        return False
    
    def _check_response_toxicity(self, response: str) -> float:
        """Check response toxicity using Detoxify"""
        if not self.detoxify_model:
            return 0.0
        
        try:
            results = self.detoxify_model.predict(response)
            max_score = max([
                results.get('toxicity', 0),
                results.get('severe_toxicity', 0),
                results.get('obscene', 0),
                results.get('threat', 0),
                results.get('insult', 0),
                results.get('identity_attack', 0)
            ])
            return float(max_score)
        except Exception as e:
            print(f"Response toxicity check error: {e}")
            return 0.0
    
    def _check_medical_compliance(self, response: str) -> bool:
        """Check if response complies with medical guidelines"""
        response_lower = response.lower()
        
        # Check if medical content needs disclaimers
        has_medical_content = any(keyword in response_lower for keyword in self.medical_keywords)
        
        if has_medical_content:
            # Check if appropriate disclaimers are present
            has_disclaimer = any(disclaimer_text in response.lower() for disclaimer_text in [
                "medical disclaimer", "consult", "healthcare provider", "professional medical advice"
            ])
            return has_disclaimer
        
        return True  # Non-medical content is compliant
    
    def _enhance_with_disclaimers(self, response: str, user_context: Dict[str, Any] = None) -> str:
        """Add appropriate medical disclaimers to response"""
        response_lower = response.lower()
        enhanced_response = response
        
        # Determine which disclaimers to add
        disclaimers_to_add = []
        
        # General medical disclaimer
        if any(keyword in response_lower for keyword in self.medical_keywords):
            if "medical disclaimer" not in response_lower:
                disclaimers_to_add.append("general")
        
        # Emergency disclaimer
        if any(word in response_lower for word in ['emergency', 'urgent', 'severe', 'critical']):
            disclaimers_to_add.append("emergency")
        
        # Medication disclaimer
        if any(word in response_lower for word in ['medication', 'prescription', 'drug', 'dosage']):
            disclaimers_to_add.append("medication")
        
        # Diagnosis disclaimer
        if any(word in response_lower for word in ['diagnosis', 'condition', 'disease', 'disorder']):
            disclaimers_to_add.append("diagnosis")
        
        # Add disclaimers
        for disclaimer_type in disclaimers_to_add:
            if disclaimer_type in self.medical_disclaimers:
                enhanced_response += self.medical_disclaimers[disclaimer_type]
        
        return enhanced_response
    
    def _add_empathy(self, response: str) -> str:
        """Add empathetic language to response"""
        # Check if response already has empathetic language
        empathy_words = ['understand', 'imagine', 'feel', 'concern', 'care']
        has_empathy = any(word in response.lower() for word in empathy_words)
        
        if not has_empathy:
            # Add empathy enhancer at the beginning
            import random
            empathy_enhancer = random.choice(self.empathy_enhancers)
            response = f"{empathy_enhancer} {response}"
        
        return response
    
    def _calculate_confidence_score(self, result: ResponseGuardResult) -> float:
        """Calculate confidence score for the validation"""
        base_confidence = 0.8
        
        # Adjust based on available models
        if self.detoxify_model:
            base_confidence += 0.1
        if self.nlp:
            base_confidence += 0.05
        
        # Adjust based on compliance
        if result.inappropriate_advice:
            base_confidence = 0.95  # High confidence in blocking
        elif result.toxicity_score > 0.5:
            base_confidence = 0.9   # High confidence in toxicity detection
        elif not result.medical_compliance:
            base_confidence = 0.85  # Medium confidence in compliance issues
        
        return min(base_confidence, 1.0)
    
    def generate_safe_fallback_response(self, user_input: str) -> str:
        """Generate a safe fallback response when original is blocked"""
        return (
            "I understand you're looking for medical information, and I want to help in a safe way. "
            "For specific medical advice, diagnosis, or treatment recommendations, I strongly encourage "
            "you to consult with a qualified healthcare provider who can properly evaluate your situation. "
            "Is there general health information I can help you with instead?"
        )
    
    def get_enhancement_summary(self, result: ResponseGuardResult) -> str:
        """Get summary of enhancements made to response"""
        if result.action_taken == "approved":
            return "✅ Response approved without modifications"
        elif result.action_taken == "enhanced":
            enhancements = []
            if result.disclaimers_added:
                enhancements.append(f"Added {len(result.disclaimers_added)} disclaimer(s)")
            if "empathy" in result.enhanced_response.lower() and "empathy" not in result.enhanced_response.lower():
                enhancements.append("Enhanced with empathetic language")
            return f"✨ Response enhanced: {', '.join(enhancements)}"
        elif result.action_taken == "blocked":
            return f"🚫 Response blocked: {', '.join(result.detected_issues)}"
        else:
            return "⚠️ Response processed with unknown action"

# Global instance
advanced_response_guard = AdvancedResponseGuard()
