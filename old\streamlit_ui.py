#!/usr/bin/env python3
"""
Streamlit UI for Secured Medical Assistant with Comprehensive Guardrails
Beautiful web interface for MongoDB + Qdrant hybrid memory system with safety features.
"""

import streamlit as st
import asyncio
import re
import uuid
from datetime import datetime
from typing import Dict, List, Any
from mcp_cli_chatbot import MCPCLIChatbot
from intelligent_feedback import feedback_system
from unified_guardrail.unified_guard_system import unified_guard_system

# 🛡️ COMPREHENSIVE GUARDRAILS IMPLEMENTATION

# 🚨 SAFETY CLASSIFIER
class SafetyClassifier:
    """Identifies and flags potentially harmful inputs or outputs"""

    def __init__(self):
        self.dangerous_medical_requests = [
            'suicide', 'kill myself', 'end my life', 'overdose', 'self harm',
            'illegal drugs', 'prescription abuse', 'drug dealing',
            'fake prescription', 'medical fraud'
        ]

        self.unsafe_language = [
            'hate speech', 'discrimination', 'violence', 'threat',
            'harassment', 'abuse', 'explicit content'
        ]

        self.medical_advice_flags = [
            'diagnose', 'cure', 'treatment plan', 'medication dosage',
            'stop taking', 'increase dose', 'medical advice'
        ]

    def classify_input(self, text: str) -> Dict[str, Any]:
        """Classify input for safety risks"""
        text_lower = text.lower()

        result = {
            'is_safe': True,
            'risk_level': 'low',
            'flags': [],
            'action': 'proceed'
        }

        # Check for dangerous medical requests
        for pattern in self.dangerous_medical_requests:
            if pattern in text_lower:
                result['is_safe'] = False
                result['risk_level'] = 'critical'
                result['flags'].append(f'dangerous_medical: {pattern}')
                result['action'] = 'block_and_escalate'

        # Check for unsafe language
        for pattern in self.unsafe_language:
            if pattern in text_lower:
                result['is_safe'] = False
                result['risk_level'] = 'high'
                result['flags'].append(f'unsafe_language: {pattern}')
                result['action'] = 'block'

        # Check for inappropriate medical advice requests
        for pattern in self.medical_advice_flags:
            if pattern in text_lower:
                result['risk_level'] = 'medium'
                result['flags'].append(f'medical_advice_request: {pattern}')
                result['action'] = 'proceed_with_disclaimer'

        return result

# 🚑 EMERGENCY TRIGGER
class EmergencyTrigger:
    """Detects life-threatening situations for immediate escalation"""

    def __init__(self):
        self.critical_symptoms = [
            'chest pain', 'heart attack', 'stroke', 'can\'t breathe',
            'difficulty breathing', 'severe bleeding', 'unconscious',
            'suicide', 'overdose', 'poisoning', 'severe burn',
            'head injury', 'spinal injury', 'allergic reaction',
            'anaphylaxis', 'seizure', 'choking'
        ]

        self.urgent_symptoms = [
            'severe pain', 'high fever', 'vomiting blood',
            'severe headache', 'vision loss', 'paralysis',
            'severe abdominal pain', 'broken bone'
        ]

        self.emergency_response = (
            "🚨 **MEDICAL EMERGENCY DETECTED** 🚨\n\n"
            "Based on your symptoms, this may require immediate medical attention.\n\n"
            "**PLEASE TAKE IMMEDIATE ACTION:**\n"
            "• Call emergency services NOW (911 in US, 999 in UK, 112 in EU)\n"
            "• Go to your nearest emergency room\n"
            "• If unable to travel, ask someone to call an ambulance\n\n"
            "**DO NOT DELAY** - Time is critical in medical emergencies.\n\n"
            "This system cannot provide emergency medical care. "
            "Professional medical intervention is required immediately."
        )

        self.urgent_response = (
            "⚠️ **URGENT MEDICAL ATTENTION NEEDED** ⚠️\n\n"
            "Your symptoms suggest you should seek medical care promptly.\n\n"
            "**RECOMMENDED ACTIONS:**\n"
            "• Contact your doctor immediately\n"
            "• Visit urgent care or emergency room\n"
            "• Do not wait for symptoms to worsen\n\n"
            "If symptoms become severe, call emergency services."
        )

    def assess_emergency_level(self, text: str) -> Dict[str, Any]:
        """Assess if input indicates medical emergency"""
        text_lower = text.lower()

        result = {
            'emergency_level': 'none',
            'triggered_symptoms': [],
            'response_override': None,
            'escalate': False,
            'log_priority': 'normal'
        }

        # Check for critical symptoms
        critical_found = []
        for symptom in self.critical_symptoms:
            if symptom in text_lower:
                critical_found.append(symptom)

        if critical_found:
            result['emergency_level'] = 'critical'
            result['triggered_symptoms'] = critical_found
            result['response_override'] = self.emergency_response
            result['escalate'] = True
            result['log_priority'] = 'critical'
            return result

        # Check for urgent symptoms
        urgent_found = []
        for symptom in self.urgent_symptoms:
            if symptom in text_lower:
                urgent_found.append(symptom)

        if urgent_found:
            result['emergency_level'] = 'urgent'
            result['triggered_symptoms'] = urgent_found
            result['response_override'] = self.urgent_response
            result['escalate'] = True
            result['log_priority'] = 'high'

        return result

# 🎯 RELEVANCE CLASSIFIER
class RelevanceClassifier:
    """Ensures inputs and memories are relevant to medical context"""

    def __init__(self):
        self.medical_keywords = [
            'pain', 'symptom', 'diagnosis', 'treatment', 'medication', 'allergy',
            'doctor', 'hospital', 'clinic', 'health', 'medical', 'illness',
            'disease', 'condition', 'therapy', 'prescription', 'appointment'
        ]

        self.off_topic_patterns = [
            'weather', 'sports', 'politics', 'entertainment', 'cooking',
            'travel', 'shopping', 'technology', 'gaming', 'social media'
        ]

    def classify_relevance(self, text: str) -> Dict[str, Any]:
        """Classify if input is relevant to medical context"""
        text_lower = text.lower()

        result = {
            'is_relevant': True,
            'relevance_score': 0.0,
            'category': 'unknown',
            'action': 'proceed'
        }

        # Calculate medical relevance score
        medical_matches = sum(1 for keyword in self.medical_keywords if keyword in text_lower)
        off_topic_matches = sum(1 for pattern in self.off_topic_patterns if pattern in text_lower)

        # Calculate relevance score (0-1)
        total_words = len(text_lower.split())
        if total_words > 0:
            result['relevance_score'] = medical_matches / total_words

        # Determine relevance
        if medical_matches > 0 and off_topic_matches == 0:
            result['is_relevant'] = True
            result['category'] = 'medical'
        elif off_topic_matches > medical_matches:
            result['is_relevant'] = False
            result['category'] = 'off_topic'
            result['action'] = 'redirect_to_medical'
        elif medical_matches == 0 and 'name' in text_lower:
            result['is_relevant'] = True
            result['category'] = 'identification'
        else:
            result['category'] = 'general'

        return result

# ✅ OUTPUT VALIDATOR
class OutputValidator:
    """Verifies LLM responses are medically accurate and compliant"""

    def __init__(self):
        self.medical_disclaimer = (
            "\n\n⚠️ **Medical Disclaimer**: This information is for educational purposes only "
            "and should not replace professional medical advice. Please consult with a "
            "qualified healthcare provider for proper diagnosis and treatment."
        )

        self.prohibited_claims = [
            'i can diagnose', 'you definitely have', 'this will cure',
            'stop taking your medication', 'increase your dose',
            'you don\'t need a doctor', 'this is medical advice'
        ]

    def validate_response(self, response: str, safety_flags: List[str] = None) -> Dict[str, Any]:
        """Validate and enhance response for compliance"""
        result = {
            'is_compliant': True,
            'enhanced_response': response,
            'flags': [],
            'disclaimers_added': []
        }

        response_lower = response.lower()

        # Check for prohibited medical claims
        for claim in self.prohibited_claims:
            if claim in response_lower:
                result['is_compliant'] = False
                result['flags'].append(f'prohibited_claim: {claim}')

        # Add disclaimers based on content and safety flags
        needs_medical_disclaimer = any([
            'symptom' in response_lower,
            'treatment' in response_lower,
            'medication' in response_lower,
            'diagnosis' in response_lower,
            safety_flags and any('medical_advice' in flag for flag in safety_flags)
        ])

        if needs_medical_disclaimer:
            result['enhanced_response'] += self.medical_disclaimer
            result['disclaimers_added'].append('medical')

        return result

# 🔒 TOOL SAFEGUARDS
class ToolSafeguards:
    """Ensures memory tools operate securely"""

    def __init__(self):
        self.max_content_length = 1000
        self.blocked_patterns = ['<script>', 'javascript:', 'sql injection', 'drop table']

    def sanitize_input(self, text: str) -> str:
        """Sanitize input to prevent injection attacks"""
        if not text:
            return ""

        # Remove potentially dangerous patterns
        sanitized = text
        for pattern in self.blocked_patterns:
            sanitized = sanitized.replace(pattern, "[BLOCKED]")

        # Limit length
        if len(sanitized) > self.max_content_length:
            sanitized = sanitized[:self.max_content_length] + "...[TRUNCATED]"

        return sanitized.strip()

# Initialize guardrails
safety_classifier = SafetyClassifier()
emergency_trigger = EmergencyTrigger()
relevance_classifier = RelevanceClassifier()
output_validator = OutputValidator()
tool_safeguards = ToolSafeguards()

# Page configuration
st.set_page_config(
    page_title="🛡️ Secured Medical Assistant",
    page_icon="🩺",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Custom CSS for beautiful styling
st.markdown("""
<style>
    /* Import Google Fonts */
    @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
    
    /* Main styling */
    .main {
        font-family: 'Inter', sans-serif;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
    }
    
    /* Header styling */
    .main-header {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        border-radius: 20px;
        padding: 2rem;
        margin: 1rem 0 2rem 0;
        text-align: center;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
    }
    
    .main-header h1 {
        color: #2c3e50;
        font-weight: 700;
        font-size: 2.5rem;
        margin: 0;
        background: linear-gradient(135deg, #667eea, #764ba2);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
    }
    
    .main-header p {
        color: #6c757d;
        font-size: 1.1rem;
        margin: 0.5rem 0 0 0;
        font-weight: 400;
    }
    
    /* Chat container */
    .chat-container {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        border-radius: 20px;
        padding: 1.5rem;
        margin: 1rem 0;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
        max-height: 500px;
        overflow-y: auto;
    }
    
    /* Chat messages */
    .chat-message {
        padding: 1rem 1.5rem;
        margin: 0.8rem 0;
        border-radius: 15px;
        font-family: 'Inter', sans-serif;
        line-height: 1.6;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    }
    
    .user-message {
        background: linear-gradient(135deg, #667eea, #764ba2);
        color: white;
        margin-left: 2rem;
        border-bottom-right-radius: 5px;
    }
    
    .assistant-message {
        background: linear-gradient(135deg, #f093fb, #f5576c);
        color: white;
        margin-right: 2rem;
        border-bottom-left-radius: 5px;
    }

    /* Ensure text visibility */
    .stMarkdown, .stText, p, h1, h2, h3, h4, h5, h6, span, div {
        color: #2c3e50 !important;
    }

    /* Sidebar text visibility */
    .css-1d391kg, .css-1d391kg p, .css-1d391kg h1, .css-1d391kg h2, .css-1d391kg h3, .css-1d391kg span {
        color: #2c3e50 !important;
    }

    /* Main content text visibility */
    .main .block-container, .main .block-container p, .main .block-container h1, .main .block-container h2, .main .block-container h3 {
        color: #2c3e50 !important;
    }

    /* Button styling for better visibility */
    .stButton > button {
        background-color: #667eea;
        color: white !important;
        border: none;
        border-radius: 5px;
        padding: 0.2rem 0.4rem;
        font-size: 0.7rem;
        min-height: 2rem;
        width: auto;
    }

    .stButton > button:hover {
        background-color: #5a6fd8;
        color: white !important;
    }
    
    /* Status cards */
    .status-card {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        border-radius: 15px;
        padding: 1.5rem;
        margin: 1rem 0;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
        text-align: center;
    }
    
    .status-card h3 {
        margin: 0 0 0.5rem 0;
        color: #2c3e50;
        font-weight: 600;
    }
    
    .status-card p {
        margin: 0;
        color: #6c757d;
        font-weight: 500;
    }
    
    .success-card {
        border-left: 4px solid #28a745;
    }
    
    .info-card {
        border-left: 4px solid #17a2b8;
    }
    
    .warning-card {
        border-left: 4px solid #ffc107;
    }
    
    /* Memory display */
    .memory-display {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        border-radius: 15px;
        padding: 1.5rem;
        margin: 1rem 0;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
        font-family: 'Monaco', 'Menlo', monospace;
        font-size: 0.9rem;
        color: #495057;
        max-height: 300px;
        overflow-y: auto;
    }
    
    /* Sidebar styling */
    .sidebar .sidebar-content {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
    }
    
    /* Button styling */
    .stButton > button {
        background: linear-gradient(135deg, #667eea, #764ba2);
        color: white;
        border: none;
        border-radius: 10px;
        padding: 0.5rem 1rem;
        font-weight: 600;
        transition: all 0.3s ease;
    }
    
    .stButton > button:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
    }
    
    /* Input styling */
    .stTextInput > div > div > input {
        border-radius: 10px;
        border: 2px solid #e9ecef;
        padding: 0.75rem;
        font-family: 'Inter', sans-serif;
    }
    
    .stTextInput > div > div > input:focus {
        border-color: #667eea;
        box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    }
</style>
""", unsafe_allow_html=True)

# Initialize session state
if 'chatbot' not in st.session_state:
    st.session_state.chatbot = None
if 'chat_messages' not in st.session_state:
    st.session_state.chat_messages = []
if 'chatbot_initialized' not in st.session_state:
    st.session_state.chatbot_initialized = False
if 'input_counter' not in st.session_state:
    st.session_state.input_counter = 0
if 'session_id' not in st.session_state:
    st.session_state.session_id = str(uuid.uuid4())
if 'message_feedback' not in st.session_state:
    st.session_state.message_feedback = {}  # Track feedback for each message
if 'last_message_id' not in st.session_state:
    st.session_state.last_message_id = None

def initialize_chatbot():
    """Initialize the secured medical chatbot with guardrails"""
    try:
        if st.session_state.chatbot is None:
            with st.spinner("🛡️ Initializing Secured Medical Assistant with Guardrails..."):
                st.session_state.chatbot = MCPCLIChatbot()
                st.session_state.chatbot_initialized = True
                st.success("✅ All guardrails activated: Safety, Emergency, Relevance, Output Validation, Tool Security")
                return True
    except Exception as e:
        st.error(f"❌ Failed to initialize chatbot: {e}")
        return False
    return True

def apply_guardrails(user_input: str) -> Dict[str, Any]:
    """Apply comprehensive guardrails to user input"""
    # 🚑 EMERGENCY TRIGGER - Highest Priority
    emergency_result = emergency_trigger.assess_emergency_level(user_input)
    if emergency_result['emergency_level'] in ['critical', 'urgent']:
        return {
            'blocked': True,
            'response': emergency_result['response_override'],
            'reason': 'emergency_detected'
        }

    # 🚨 SAFETY CLASSIFICATION
    safety_result = safety_classifier.classify_input(user_input)
    if not safety_result['is_safe']:
        if safety_result['action'] == 'block_and_escalate':
            response = (
                "I'm concerned about your message and want to help. If you're having thoughts of "
                "self-harm, please contact a mental health professional immediately or call a crisis hotline. "
                "In the US: 988 Suicide & Crisis Lifeline. Your safety is important."
            )
        else:
            response = (
                "I'm here to help with medical questions and concerns. Please keep our conversation "
                "focused on health-related topics so I can assist you better."
            )
        return {
            'blocked': True,
            'response': response,
            'reason': 'safety_violation'
        }

    # 🎯 RELEVANCE CHECK
    relevance_result = relevance_classifier.classify_relevance(user_input)
    if not relevance_result['is_relevant'] and relevance_result['action'] == 'redirect_to_medical':
        response = (
            "I'm a medical assistant designed to help with health-related questions and concerns. "
            "Could you please share any medical symptoms, conditions, or health questions you have? "
            "I'm here to provide information and support for your healthcare needs."
        )
        return {
            'blocked': True,
            'response': response,
            'reason': 'off_topic'
        }

    # 🔒 TOOL SAFEGUARDS
    sanitized_input = tool_safeguards.sanitize_input(user_input)

    return {
        'blocked': False,
        'sanitized_input': sanitized_input,
        'safety_flags': safety_result.get('flags', [])
    }

def validate_output(response: str, safety_flags: List[str] = None) -> str:
    """Validate and enhance output with compliance checks"""
    validation_result = output_validator.validate_response(response, safety_flags)

    if not validation_result['is_compliant']:
        # Return a safe, compliant response
        return (
            "I understand your concern and I'm here to help with general health information. "
            "For specific medical advice, diagnosis, or treatment recommendations, "
            "please consult with a qualified healthcare provider who can properly "
            "evaluate your condition."
        )

    return validation_result['enhanced_response']

def display_chat_messages():
    """Display chat messages with beautiful styling and feedback buttons"""
    for i, message in enumerate(st.session_state.chat_messages):
        if message["role"] == "user":
            st.markdown(f'''
            <div class="chat-message user-message">
                <strong>👤 You:</strong><br>{message["content"]}
            </div>
            ''', unsafe_allow_html=True)
        else:
            # Assistant message with feedback buttons
            message_id = message.get("id", f"msg_{i}")

            st.markdown(f'''
            <div class="chat-message assistant-message">
                <strong>🛡️ Secured Assistant:</strong><br>{message["content"]}
            </div>
            ''', unsafe_allow_html=True)

            # Add feedback buttons and comment box for assistant messages
            col1, col2, col3, col4 = st.columns([0.5, 0.5, 1, 7])

            with col1:
                if st.button("👍", key=f"thumbs_up_{message_id}", help="Good response",
                           use_container_width=False):
                    # Show comment box for positive feedback
                    st.session_state[f"show_comment_{message_id}"] = True
                    st.session_state[f"feedback_type_{message_id}"] = "up"
                    st.rerun()

            with col2:
                if st.button("👎", key=f"thumbs_down_{message_id}", help="Poor response",
                           use_container_width=False):
                    # Show comment box for negative feedback
                    st.session_state[f"show_comment_{message_id}"] = True
                    st.session_state[f"feedback_type_{message_id}"] = "down"
                    st.rerun()

            with col3:
                # Show feedback status
                if message_id in st.session_state.message_feedback:
                    feedback_data = st.session_state.message_feedback[message_id]
                    if feedback_data["feedback"] == "up":
                        st.markdown("✅ Liked")
                    else:
                        st.markdown("❌ Disliked")

            # Show comment box if feedback button was clicked
            if st.session_state.get(f"show_comment_{message_id}", False):
                feedback_type = st.session_state.get(f"feedback_type_{message_id}", "up")

                with st.form(key=f"feedback_form_{message_id}"):
                    st.markdown(f"**{'👍 What did you like?' if feedback_type == 'up' else '👎 What could be improved?'}**")

                    feedback_comment = st.text_area(
                        "Your feedback helps me learn and improve:",
                        placeholder="e.g., 'Too detailed', 'Not empathetic enough', 'Very helpful', 'Too clinical'...",
                        height=80,
                        key=f"comment_{message_id}"
                    )

                    col_submit, col_skip = st.columns([1, 1])

                    with col_submit:
                        if st.form_submit_button("Submit Feedback", type="primary"):
                            handle_feedback(message_id, feedback_type, message["content"], feedback_comment)
                            st.session_state[f"show_comment_{message_id}"] = False
                            st.rerun()

                    with col_skip:
                        if st.form_submit_button("Skip Comment"):
                            handle_feedback(message_id, feedback_type, message["content"], "")
                            st.session_state[f"show_comment_{message_id}"] = False
                            st.rerun()

def handle_feedback(message_id: str, feedback: str, bot_response: str, feedback_comment: str = ""):
    """Handle user feedback with LLM-based learning"""
    try:
        # Store feedback in session state with comment
        st.session_state.message_feedback[message_id] = {
            "feedback": feedback,
            "comment": feedback_comment,
            "timestamp": datetime.now().isoformat()
        }

        # Get the corresponding user message
        user_message = ""
        for i, msg in enumerate(st.session_state.chat_messages):
            if msg.get("id") == message_id and i > 0:
                user_message = st.session_state.chat_messages[i-1]["content"]
                break

        # Get patient name if available
        patient_name = ""
        if st.session_state.chatbot and hasattr(st.session_state.chatbot, 'patient_name'):
            patient_name = st.session_state.chatbot.patient_name

        # Save feedback to the intelligent system with comment
        success = feedback_system.save_feedback(
            session_id=st.session_state.session_id,
            message_id=message_id,
            user_message=user_message,
            bot_response=bot_response,
            feedback=feedback,
            feedback_comment=feedback_comment,
            patient_name=patient_name
        )

        if success:
            if feedback == "up":
                if feedback_comment:
                    st.success(f"👍 Thank you! Your comment: '{feedback_comment}' helps me understand what you like.")
                else:
                    st.success("👍 Thank you for the positive feedback! I'm learning your preferences.")
            else:
                if feedback_comment:
                    st.info(f"👎 Thank you for the feedback! I'll work on: '{feedback_comment}'")
                else:
                    st.info("👎 Thank you for the feedback. I'll try to improve my responses.")

        # Show learning insights if available
        user_prefs = feedback_system.get_user_preferences(st.session_state.session_id)
        if user_prefs and user_prefs.total_feedback_count > 2:
            st.caption(f"🧠 Learning: I now prefer {user_prefs.preferred_response_style} responses based on your feedback.")

    except Exception as e:
        st.error(f"Error saving feedback: {e}")

def display_patient_info():
    """Display patient information in beautiful cards"""
    if st.session_state.chatbot and st.session_state.chatbot.has_name:
        col1, col2, col3 = st.columns(3)
        
        with col1:
            st.markdown(f'''
            <div class="status-card success-card">
                <h3>👤 Patient</h3>
                <p>{st.session_state.chatbot.patient_name}</p>
            </div>
            ''', unsafe_allow_html=True)
        
        with col2:
            st.markdown(f'''
            <div class="status-card info-card">
                <h3>💬 Exchanges</h3>
                <p>{len(st.session_state.chatbot.conversation_history)}</p>
            </div>
            ''', unsafe_allow_html=True)
        
        with col3:
            guardrails_status = "🟢 Active" if st.session_state.chatbot_initialized else "🔴 Inactive"
            st.markdown(f'''
            <div class="status-card warning-card">
                <h3>🛡️ Guardrails</h3>
                <p>{guardrails_status}</p>
            </div>
            ''', unsafe_allow_html=True)

async def display_memory_context():
    """Display patient memory context"""
    if st.session_state.chatbot and st.session_state.chatbot.has_name:
        try:
            context = await st.session_state.chatbot.get_patient_context()
            if context and context.strip():
                st.markdown(f'''
                <div class="memory-display">
                    <strong>🧠 Patient Memory Context:</strong><br><br>
                    {context.replace(chr(10), '<br>')}
                </div>
                ''', unsafe_allow_html=True)
            else:
                st.markdown(f'''
                <div class="memory-display">
                    <strong>⏳ Building memory for {st.session_state.chatbot.patient_name}...</strong><br>
                    Share symptoms or medical information to see the hybrid memory system in action!
                </div>
                ''', unsafe_allow_html=True)
        except Exception as e:
            st.markdown(f'''
            <div class="memory-display">
                <strong>⚠️ Memory System Status:</strong><br>
                Error: {str(e)}<br>
                <em>Make sure MongoDB and Qdrant are accessible.</em>
            </div>
            ''', unsafe_allow_html=True)

def main():
    """Main Streamlit application"""
    
    # Header
    st.markdown('''
    <div class="main-header">
        <h1>🛡️ Secured Medical Assistant</h1>
        <p>Hybrid Memory System with Comprehensive Guardrails</p>
    </div>
    ''', unsafe_allow_html=True)
    
    # Sidebar
    with st.sidebar:
        st.markdown("### 🛡️ Secured Control Panel")

        # Initialize chatbot button
        if st.button("🚀 Initialize Secured System", type="primary"):
            initialize_chatbot()

        # Status display
        if st.session_state.chatbot_initialized:
            st.success("● Secured System Online")
        else:
            st.error("● System Offline")

        st.markdown("---")

        # Guardrails status
        st.markdown("### 🛡️ Active Guardrails")
        st.markdown("""
        **🚨 Safety Classifier**
        ✅ Harmful content detection

        **🔒 Tool Safeguards**
        ✅ Memory security & validation

        **🎯 Relevance Classifier**
        ✅ Medical context filtering

        **✅ Output Validation**
        ✅ Response compliance & disclaimers

        **🚑 Emergency Trigger**
        ✅ Life-threatening detection
        """)

        st.markdown("---")

        # Memory systems status
        st.markdown("### 🧠 Memory Systems")
        st.markdown("""
        **📅 Episodic Memory**
        MongoDB - Visit records

        **🧠 Semantic Memory**
        Qdrant - Medical knowledge

        **👤 Behavioral Memory**
        MongoDB - Patient preferences
        """)

        st.markdown("---")

        # New session button
        if st.button("🔄 New Session"):
            if st.session_state.chatbot:
                asyncio.run(st.session_state.chatbot.cleanup())
            st.session_state.chatbot = None
            st.session_state.chat_messages = []
            st.session_state.chatbot_initialized = False
            st.session_state.input_counter = 0
            st.rerun()

        st.markdown("---")

        # Test scenarios with guardrails
        st.markdown("### 🧪 Test Guardrails")

        col1, col2 = st.columns(2)
        with col1:
            if st.button("🚨 Emergency Test"):
                st.session_state.test_input = "I'm having severe chest pain"
                st.rerun()
        with col2:
            if st.button("🚫 Safety Test"):
                st.session_state.test_input = "I want to hurt myself"
                st.rerun()

        st.markdown("### 📊 Learning Analytics")

        # Get user preferences and feedback analytics
        user_prefs = feedback_system.get_user_preferences(st.session_state.session_id)
        if user_prefs:
            st.markdown(f"""
            **Your Preferences:**
            - **Style**: {user_prefs.preferred_response_style}
            - **Tone**: {user_prefs.tone_preference}
            - **Feedback Given**: {user_prefs.total_feedback_count}
            - **Satisfaction**: {(user_prefs.positive_feedback_count / max(user_prefs.total_feedback_count, 1) * 100):.1f}%
            """)
        else:
            st.markdown("*No preferences learned yet. Use 👍👎 to help me learn!*")

        st.markdown("---")

        st.markdown("### 👥 Sample Patients")
        st.markdown("""
        **Try these examples:**
        - "Hi, I'm John. I have diabetes and chest pain, 8/10."
        - "I'm allergic to penicillin and prefer morning appointments."
        - "My back hurts and I take aspirin daily."
        """)
    
    # Main content area
    if not st.session_state.chatbot_initialized:
        st.markdown('''
        <div class="status-card info-card">
            <h3>🛡️ Welcome to Secured Medical Assistant!</h3>
            <p>Click <strong>"Initialize Secured System"</strong> in the sidebar to start.</p>
            <br>
            <p><strong>Features:</strong></p>
            <ul style="text-align: left; display: inline-block;">
                <li>🛡️ Comprehensive safety guardrails</li>
                <li>📊 MongoDB for episodic & behavioral memory</li>
                <li>🧠 Qdrant for semantic search</li>
                <li>🤖 LLM-powered natural language understanding</li>
                <li>💬 Real-time memory updates</li>
                <li>🚨 Emergency detection & safety filtering</li>
                <li>✅ Medical compliance & disclaimers</li>
            </ul>
        </div>
        ''', unsafe_allow_html=True)
        return
    
    # Patient information display
    display_patient_info()
    
    # Memory context display
    asyncio.run(display_memory_context())
    
    # Chat interface
    st.markdown("### 💬 Conversation")
    
    # Chat container
    with st.container():
        st.markdown('<div class="chat-container">', unsafe_allow_html=True)
        
        # Display chat messages
        if st.session_state.chat_messages:
            display_chat_messages()
        else:
            st.markdown(f'''
            <div class="chat-message assistant-message">
                <strong>🛡️ Secured Assistant:</strong><br>
                Hello! I'm your secured medical assistant with comprehensive safety guardrails. What's your name?
            </div>
            ''', unsafe_allow_html=True)
        
        st.markdown('</div>', unsafe_allow_html=True)
    
    # Chat input
    user_input = st.text_input(
        "💬 Type your message:",
        placeholder="Tell me your name or describe your health concerns...",
        key=f"chat_input_{st.session_state.input_counter}",
        help="💡 Press Enter or click Send to submit your message"
    )
    
    # Send button
    col1, col2, col3 = st.columns([1, 1, 4])
    with col1:
        send_button = st.button("📤 Send", type="primary")
    with col2:
        if st.button("🗑️ Clear Chat"):
            st.session_state.chat_messages = []
            st.rerun()
    
    # Handle test inputs from sidebar
    if hasattr(st.session_state, 'test_input'):
        user_input = st.session_state.test_input
        delattr(st.session_state, 'test_input')
        send_button = True

    # Process message
    if (send_button and user_input) or (user_input and user_input != st.session_state.get('last_input', '')):
        # Add user message
        st.session_state.chat_messages.append({"role": "user", "content": user_input})

        # Apply comprehensive guardrails
        with st.spinner("🛡️ Processing with comprehensive guardrails..."):
            try:
                # Apply unified advanced guardrails
                should_proceed, sanitized_input, safety_message = unified_guard_system.process_input_only(
                    user_input, {"session_id": st.session_state.session_id}
                )

                if not should_proceed:
                    # Input blocked by guardrails
                    st.session_state.chat_messages.append({"role": "assistant", "content": safety_message})
                else:
                    # Input passed guardrails - process with chatbot

                    # Generate adaptive prompt based on user preferences
                    adaptive_prompt = feedback_system.generate_adaptive_prompt(
                        st.session_state.session_id,
                        sanitized_input
                    )

                    # Use asyncio to handle the async chatbot
                    loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(loop)

                    # Process with adaptive prompt (if chatbot supports it)
                    if hasattr(st.session_state.chatbot, 'process_message_with_prompt'):
                        raw_response = loop.run_until_complete(
                            st.session_state.chatbot.process_message_with_prompt(sanitized_input, adaptive_prompt)
                        )
                    else:
                        raw_response = loop.run_until_complete(st.session_state.chatbot.process_message(sanitized_input))

                    loop.close()

                    # Apply response guardrails
                    is_safe, final_response, enhancement_summary = unified_guard_system.process_response_only(
                        raw_response, {"session_id": st.session_state.session_id}
                    )

                    # Generate unique message ID
                    message_id = str(uuid.uuid4())
                    st.session_state.last_message_id = message_id

                    st.session_state.chat_messages.append({
                        "role": "assistant",
                        "content": final_response,
                        "id": message_id
                    })

                    # Show enhancement summary if response was modified
                    if "enhanced" in enhancement_summary.lower():
                        st.caption(f"🛡️ {enhancement_summary}")

                # Clear input by incrementing counter
                st.session_state.input_counter += 1
                st.session_state.last_input = user_input

                # Force UI refresh
                st.rerun()
            except Exception as e:
                st.error(f"❌ Error processing message: {e}")
    
    # Feedback Learning Summary
    if st.session_state.chat_messages:
        st.markdown("---")
        st.markdown("### 🧠 Learning Summary")

        col1, col2, col3 = st.columns(3)

        with col1:
            feedback_count = len(st.session_state.message_feedback)
            st.metric("Feedback Given", feedback_count)

        with col2:
            positive_feedback = sum(1 for f in st.session_state.message_feedback.values()
                                  if isinstance(f, dict) and f.get("feedback") == "up" or f == "up")
            st.metric("Positive Feedback", positive_feedback)

        with col3:
            user_prefs = feedback_system.get_user_preferences(st.session_state.session_id)
            if user_prefs:
                st.metric("Preferred Style", user_prefs.preferred_response_style.title())
            else:
                st.metric("Preferred Style", "Learning...")

        # Show learning progress
        if feedback_count > 0:
            satisfaction_rate = (positive_feedback / feedback_count) * 100
            st.progress(satisfaction_rate / 100)
            st.caption(f"Satisfaction Rate: {satisfaction_rate:.1f}%")

    # Footer
    st.markdown("---")
    st.markdown('''
    <div style="text-align: center; color: #6c757d; font-size: 0.9rem; padding: 1rem;">
        🛡️ Secured Medical Assistant | Comprehensive Guardrails + Intelligent Feedback Learning + Hybrid Memory
    </div>
    ''', unsafe_allow_html=True)

if __name__ == "__main__":
    main()
