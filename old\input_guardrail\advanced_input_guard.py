#!/usr/bin/env python3
"""
Advanced Input Guardrails using Presidio Analyzer and Detoxify
Comprehensive input validation and safety checking
"""

import os
import re
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
from datetime import datetime

# Advanced libraries for guardrails
try:
    from presidio_analyzer import AnalyzerEngine
    from presidio_anonymizer import AnonymizerEngine
    PRESIDIO_AVAILABLE = True
except ImportError:
    PRESIDIO_AVAILABLE = False
    print("⚠️ Presidio not available. Install with: pip install presidio-analyzer presidio-anonymizer")

try:
    from detoxify import Detoxify
    DETOXIFY_AVAILABLE = True
except ImportError:
    DETOXIFY_AVAILABLE = False
    print("⚠️ Detoxify not available. Install with: pip install detoxify")

try:
    import torch
    TORCH_AVAILABLE = True
except ImportError:
    TORCH_AVAILABLE = False
    print("⚠️ PyTorch not available. Install with: pip install torch")

@dataclass
class InputGuardResult:
    """Result of input guardrail analysis"""
    is_safe: bool
    risk_level: str  # "low", "medium", "high", "critical"
    detected_issues: List[str]
    pii_detected: List[str]
    toxicity_score: float
    medical_emergency: bool
    action_required: str  # "allow", "sanitize", "block", "escalate"
    sanitized_input: str
    confidence_score: float

class AdvancedInputGuard:
    """Advanced input guardrails using ML models and NLP libraries"""
    
    def __init__(self):
        self.analyzer_engine = None
        self.anonymizer_engine = None
        self.detoxify_model = None
        
        # Initialize Presidio for PII detection
        if PRESIDIO_AVAILABLE:
            try:
                self.analyzer_engine = AnalyzerEngine()
                self.anonymizer_engine = AnonymizerEngine()
                print("✅ Presidio PII detection initialized")
            except Exception as e:
                print(f"⚠️ Presidio initialization failed: {e}")
        
        # Initialize Detoxify for toxicity detection
        if DETOXIFY_AVAILABLE and TORCH_AVAILABLE:
            try:
                self.detoxify_model = Detoxify('original')
                print("✅ Detoxify toxicity detection initialized")
            except Exception as e:
                print(f"⚠️ Detoxify initialization failed: {e}")
        
        # Medical emergency patterns
        self.emergency_patterns = [
            r'\b(?:chest pain|heart attack|stroke|can\'?t breathe|difficulty breathing)\b',
            r'\b(?:severe bleeding|unconscious|suicide|overdose|poisoning)\b',
            r'\b(?:severe burn|head injury|spinal injury|allergic reaction)\b',
            r'\b(?:anaphylaxis|seizure|choking|cardiac arrest)\b'
        ]
        
        # High-risk medical patterns
        self.high_risk_patterns = [
            r'\b(?:kill myself|end my life|want to die|suicide)\b',
            r'\b(?:illegal drugs|drug dealing|fake prescription)\b',
            r'\b(?:medical fraud|prescription abuse)\b'
        ]
        
        # Sensitive medical information patterns
        self.sensitive_medical_patterns = [
            r'\b(?:HIV|AIDS|cancer|mental illness|depression)\b',
            r'\b(?:addiction|substance abuse|eating disorder)\b',
            r'\b(?:sexual health|STD|pregnancy)\b'
        ]
    
    def analyze_input(self, text: str, user_context: Dict[str, Any] = None) -> InputGuardResult:
        """Comprehensive input analysis using multiple guardrails"""
        
        # Initialize result
        result = InputGuardResult(
            is_safe=True,
            risk_level="low",
            detected_issues=[],
            pii_detected=[],
            toxicity_score=0.0,
            medical_emergency=False,
            action_required="allow",
            sanitized_input=text,
            confidence_score=0.0
        )
        
        # 1. Emergency Detection (Highest Priority)
        emergency_detected = self._detect_medical_emergency(text)
        if emergency_detected:
            result.medical_emergency = True
            result.risk_level = "critical"
            result.action_required = "escalate"
            result.detected_issues.append("medical_emergency")
            result.is_safe = False
            return result
        
        # 2. High-Risk Content Detection
        high_risk_detected = self._detect_high_risk_content(text)
        if high_risk_detected:
            result.risk_level = "high"
            result.action_required = "block"
            result.detected_issues.extend(high_risk_detected)
            result.is_safe = False
        
        # 3. PII Detection using Presidio
        pii_results = self._detect_pii(text)
        if pii_results:
            result.pii_detected = pii_results
            result.detected_issues.append("pii_detected")
            result.sanitized_input = self._sanitize_pii(text)
            if result.risk_level == "low":
                result.risk_level = "medium"
                result.action_required = "sanitize"
        
        # 4. Toxicity Detection using Detoxify
        toxicity_score = self._detect_toxicity(text)
        result.toxicity_score = toxicity_score
        if toxicity_score > 0.7:
            result.risk_level = "high"
            result.action_required = "block"
            result.detected_issues.append("high_toxicity")
            result.is_safe = False
        elif toxicity_score > 0.4:
            result.risk_level = "medium"
            result.detected_issues.append("moderate_toxicity")
            if result.action_required == "allow":
                result.action_required = "sanitize"
        
        # 5. Sensitive Medical Content Detection
        sensitive_detected = self._detect_sensitive_medical_content(text)
        if sensitive_detected:
            result.detected_issues.extend(sensitive_detected)
            if result.risk_level == "low":
                result.risk_level = "medium"
        
        # 6. Calculate confidence score
        result.confidence_score = self._calculate_confidence_score(result)
        
        return result
    
    def _detect_medical_emergency(self, text: str) -> bool:
        """Detect medical emergencies using pattern matching"""
        text_lower = text.lower()
        for pattern in self.emergency_patterns:
            if re.search(pattern, text_lower, re.IGNORECASE):
                return True
        return False
    
    def _detect_high_risk_content(self, text: str) -> List[str]:
        """Detect high-risk content patterns"""
        detected = []
        text_lower = text.lower()
        
        for pattern in self.high_risk_patterns:
            if re.search(pattern, text_lower, re.IGNORECASE):
                detected.append("high_risk_content")
                break
        
        return detected
    
    def _detect_pii(self, text: str) -> List[str]:
        """Detect PII using Presidio Analyzer"""
        if not self.analyzer_engine:
            return []
        
        try:
            results = self.analyzer_engine.analyze(
                text=text,
                entities=["PERSON", "EMAIL_ADDRESS", "PHONE_NUMBER", "SSN", "CREDIT_CARD", "IBAN_CODE"],
                language='en'
            )
            
            detected_pii = []
            for result in results:
                detected_pii.append(f"{result.entity_type}:{result.score:.2f}")
            
            return detected_pii
        except Exception as e:
            print(f"PII detection error: {e}")
            return []
    
    def _sanitize_pii(self, text: str) -> str:
        """Sanitize PII using Presidio Anonymizer"""
        if not self.anonymizer_engine or not self.analyzer_engine:
            return text
        
        try:
            # Analyze first
            analyzer_results = self.analyzer_engine.analyze(
                text=text,
                entities=["PERSON", "EMAIL_ADDRESS", "PHONE_NUMBER", "SSN", "CREDIT_CARD"],
                language='en'
            )
            
            # Anonymize
            anonymized_result = self.anonymizer_engine.anonymize(
                text=text,
                analyzer_results=analyzer_results
            )
            
            return anonymized_result.text
        except Exception as e:
            print(f"PII sanitization error: {e}")
            return text
    
    def _detect_toxicity(self, text: str) -> float:
        """Detect toxicity using Detoxify model"""
        if not self.detoxify_model:
            return 0.0
        
        try:
            results = self.detoxify_model.predict(text)
            # Return the maximum toxicity score across all categories
            max_score = max([
                results.get('toxicity', 0),
                results.get('severe_toxicity', 0),
                results.get('obscene', 0),
                results.get('threat', 0),
                results.get('insult', 0),
                results.get('identity_attack', 0)
            ])
            return float(max_score)
        except Exception as e:
            print(f"Toxicity detection error: {e}")
            return 0.0
    
    def _detect_sensitive_medical_content(self, text: str) -> List[str]:
        """Detect sensitive medical content"""
        detected = []
        text_lower = text.lower()
        
        for pattern in self.sensitive_medical_patterns:
            if re.search(pattern, text_lower, re.IGNORECASE):
                detected.append("sensitive_medical_content")
                break
        
        return detected
    
    def _calculate_confidence_score(self, result: InputGuardResult) -> float:
        """Calculate confidence score for the analysis"""
        base_confidence = 0.8
        
        # Adjust based on available models
        if self.detoxify_model:
            base_confidence += 0.1
        if self.analyzer_engine:
            base_confidence += 0.1
        
        # Adjust based on detection results
        if result.medical_emergency:
            base_confidence = 0.95
        elif result.toxicity_score > 0.7:
            base_confidence = 0.9
        elif len(result.detected_issues) > 2:
            base_confidence = 0.85
        
        return min(base_confidence, 1.0)
    
    def get_safety_recommendation(self, result: InputGuardResult) -> str:
        """Get safety recommendation based on analysis"""
        if result.medical_emergency:
            return "🚨 MEDICAL EMERGENCY DETECTED - Immediate escalation required"
        elif result.risk_level == "high":
            return "🚫 HIGH RISK - Content blocked for safety"
        elif result.risk_level == "medium":
            return "⚠️ MEDIUM RISK - Content sanitized and monitored"
        else:
            return "✅ LOW RISK - Content approved"

# Global instance
advanced_input_guard = AdvancedInputGuard()
