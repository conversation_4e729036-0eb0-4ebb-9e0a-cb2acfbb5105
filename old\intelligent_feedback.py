#!/usr/bin/env python3
"""
Intelligent Feedback System for Medical Chatbot
Learns from user preferences to improve response quality over time.
"""

import json
import uuid
import os
from datetime import datetime
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, asdict
from collections import defaultdict

# MongoDB integration for persistent storage
try:
    from pymongo import MongoClient
    MONGO_AVAILABLE = True
except ImportError:
    MONGO_AVAILABLE = False

from dotenv import load_dotenv
load_dotenv()

@dataclass
class FeedbackEntry:
    """Individual feedback entry with LLM-based learning"""
    session_id: str
    message_id: str
    user_message: str
    bot_response: str
    feedback: str  # "up" or "down"
    feedback_comment: str  # User's detailed feedback comment
    timestamp: str
    response_type: str  # "empathetic", "clinical", "detailed", "brief"
    patient_name: str = ""
    llm_analysis: str = ""  # LLM analysis of the feedback
    improvement_suggestions: List[str] = None

@dataclass
class UserPreferences:
    """Learned user preferences"""
    session_id: str
    patient_name: str = ""
    preferred_response_style: str = "balanced"  # "empathetic", "clinical", "detailed", "brief"
    response_length_preference: str = "medium"  # "short", "medium", "long"
    tone_preference: str = "caring"  # "caring", "professional", "casual"
    total_feedback_count: int = 0
    positive_feedback_count: int = 0
    last_updated: str = ""

class IntelligentFeedbackSystem:
    """Advanced feedback system that learns and adapts"""
    
    def __init__(self):
        self.feedback_storage = {}  # In-memory for quick access
        self.user_preferences = {}  # Session-based preferences
        self.response_patterns = self._initialize_response_patterns()
        
        # MongoDB connection for persistent storage
        self.mongo_available = False
        if MONGO_AVAILABLE:
            try:
                mongodb_uri = os.getenv("MongoDB_URI")
                if mongodb_uri:
                    self.mongo_client = MongoClient(mongodb_uri)
                    self.db = self.mongo_client["Medical_Records"]
                    self.feedback_collection = self.db["feedback"]
                    self.preferences_collection = self.db["user_preferences"]
                    self.mongo_available = True
                    print("✅ Feedback system connected to MongoDB")
                else:
                    print("⚠️ MongoDB URI not found, using in-memory storage")
            except Exception as e:
                print(f"⚠️ MongoDB connection failed: {e}")
        else:
            print("⚠️ PyMongo not installed, using in-memory storage")
        
        # Load existing preferences
        self._load_preferences()
    
    def _initialize_response_patterns(self) -> Dict[str, Dict[str, str]]:
        """Initialize different response patterns for learning"""
        return {
            "empathetic": {
                "greeting": "I understand this must be concerning for you.",
                "symptom_response": "I can imagine how uncomfortable that must be.",
                "advice_intro": "Here's what might help, though please remember to consult your doctor:",
                "closing": "I hope this information helps. Please take care of yourself."
            },
            "clinical": {
                "greeting": "I'll provide you with medical information.",
                "symptom_response": "Based on the symptoms you've described:",
                "advice_intro": "Medical recommendations include:",
                "closing": "Please consult a healthcare professional for proper evaluation."
            },
            "detailed": {
                "explanation_style": "comprehensive with background information",
                "include_causes": True,
                "include_prevention": True,
                "include_when_to_seek_help": True
            },
            "brief": {
                "explanation_style": "concise and direct",
                "include_causes": False,
                "include_prevention": False,
                "include_when_to_seek_help": True
            }
        }
    
    def _load_preferences(self):
        """Load user preferences from persistent storage"""
        if self.mongo_available:
            try:
                preferences = self.preferences_collection.find()
                for pref in preferences:
                    session_id = pref.get("session_id")
                    if session_id:
                        # Remove MongoDB _id field
                        pref.pop("_id", None)
                        self.user_preferences[session_id] = UserPreferences(**pref)
            except Exception as e:
                print(f"Error loading preferences: {e}")
    
    def save_feedback(self, session_id: str, message_id: str, user_message: str,
                     bot_response: str, feedback: str, feedback_comment: str = "", patient_name: str = "") -> bool:
        """Save feedback and update user preferences with LLM-based learning"""
        try:
            # Analyze response type
            response_type = self._analyze_response_type(bot_response)

            # Perform LLM-based analysis of feedback
            llm_analysis = self._analyze_feedback_with_llm(user_message, bot_response, feedback, feedback_comment)
            improvement_suggestions = self._generate_improvement_suggestions(user_message, bot_response, feedback, feedback_comment)

            # Create feedback entry
            feedback_entry = FeedbackEntry(
                session_id=session_id,
                message_id=message_id,
                user_message=user_message,
                bot_response=bot_response,
                feedback=feedback,
                feedback_comment=feedback_comment,
                timestamp=datetime.now().isoformat(),
                response_type=response_type,
                patient_name=patient_name,
                llm_analysis=llm_analysis,
                improvement_suggestions=improvement_suggestions
            )
            
            # Store in memory
            if session_id not in self.feedback_storage:
                self.feedback_storage[session_id] = []
            self.feedback_storage[session_id].append(feedback_entry)
            
            # Store in MongoDB
            if self.mongo_available:
                self.feedback_collection.insert_one(asdict(feedback_entry))
            
            # Update user preferences
            self._update_user_preferences(session_id, feedback_entry)
            
            print(f"📝 Feedback saved: {feedback} for response type: {response_type}")
            return True
            
        except Exception as e:
            print(f"Error saving feedback: {e}")
            return False
    
    def _analyze_response_type(self, response: str) -> str:
        """Analyze the type of response to categorize it"""
        response_lower = response.lower()
        
        # Check for empathetic language
        empathetic_words = ["understand", "imagine", "concerning", "care", "sorry", "feel"]
        empathetic_score = sum(1 for word in empathetic_words if word in response_lower)
        
        # Check for clinical language
        clinical_words = ["medical", "diagnosis", "treatment", "symptoms", "condition", "healthcare"]
        clinical_score = sum(1 for word in clinical_words if word in response_lower)
        
        # Check response length
        word_count = len(response.split())
        
        if empathetic_score > clinical_score:
            return "empathetic"
        elif clinical_score > empathetic_score:
            return "clinical"
        elif word_count > 100:
            return "detailed"
        else:
            return "brief"
    
    def _update_user_preferences(self, session_id: str, feedback_entry: FeedbackEntry):
        """Update user preferences based on feedback"""
        # Get or create user preferences
        if session_id not in self.user_preferences:
            self.user_preferences[session_id] = UserPreferences(
                session_id=session_id,
                patient_name=feedback_entry.patient_name
            )
        
        prefs = self.user_preferences[session_id]
        prefs.total_feedback_count += 1
        prefs.last_updated = datetime.now().isoformat()
        
        if feedback_entry.feedback == "up":
            prefs.positive_feedback_count += 1
            # Learn from positive feedback
            if feedback_entry.response_type in ["empathetic", "clinical", "detailed", "brief"]:
                prefs.preferred_response_style = feedback_entry.response_type
        
        # Update patient name if provided
        if feedback_entry.patient_name:
            prefs.patient_name = feedback_entry.patient_name
        
        # Save to MongoDB
        if self.mongo_available:
            try:
                self.preferences_collection.replace_one(
                    {"session_id": session_id},
                    asdict(prefs),
                    upsert=True
                )
            except Exception as e:
                print(f"Error saving preferences: {e}")
    
    def get_user_preferences(self, session_id: str) -> Optional[UserPreferences]:
        """Get user preferences for a session"""
        return self.user_preferences.get(session_id)
    
    def generate_adaptive_prompt(self, session_id: str, user_message: str) -> str:
        """Generate an adaptive prompt based on user preferences"""
        prefs = self.get_user_preferences(session_id)
        
        if not prefs:
            # Default prompt for new users
            return self._get_default_prompt(user_message)
        
        # Adaptive prompt based on learned preferences
        base_prompt = f"""
        You are a medical assistant helping {prefs.patient_name or 'the patient'}. 
        
        User message: "{user_message}"
        
        Based on this user's feedback history, they prefer:
        - Response style: {prefs.preferred_response_style}
        - Tone: {prefs.tone_preference}
        - Length: {prefs.response_length_preference}
        
        """
        
        # Add style-specific instructions
        if prefs.preferred_response_style == "empathetic":
            base_prompt += """
            Respond with empathy and understanding. Use phrases like:
            - "I understand this must be concerning"
            - "I can imagine how you're feeling"
            - "That sounds difficult"
            Show genuine care and emotional support.
            """
        elif prefs.preferred_response_style == "clinical":
            base_prompt += """
            Respond in a professional, clinical manner. Focus on:
            - Medical facts and information
            - Clear, direct language
            - Professional terminology when appropriate
            - Structured, logical responses
            """
        elif prefs.preferred_response_style == "detailed":
            base_prompt += """
            Provide comprehensive, detailed responses including:
            - Background information
            - Possible causes
            - Prevention tips
            - When to seek medical help
            """
        elif prefs.preferred_response_style == "brief":
            base_prompt += """
            Keep responses concise and to the point:
            - Direct answers
            - Essential information only
            - Clear action items
            """
        
        base_prompt += """
        Always include appropriate medical disclaimers and encourage consulting healthcare professionals.
        """
        
        return base_prompt
    
    def _get_default_prompt(self, user_message: str) -> str:
        """Default prompt for new users"""
        return f"""
        You are a caring medical assistant. The user said: "{user_message}"
        
        Provide a balanced response that is:
        - Empathetic and caring
        - Medically informative
        - Appropriately detailed
        - Includes medical disclaimers
        
        Encourage the user to consult healthcare professionals for proper medical advice.
        """

    def _analyze_feedback_with_llm(self, user_message: str, bot_response: str, feedback: str, feedback_comment: str) -> str:
        """Use LLM to analyze feedback and understand user preferences"""
        try:
            # Simple LLM-style analysis (can be replaced with actual LLM call)
            analysis_prompt = f"""
            Analyze this medical chatbot interaction and feedback:

            User Message: "{user_message}"
            Bot Response: "{bot_response}"
            User Feedback: {feedback} (thumbs up/down)
            User Comment: "{feedback_comment}"

            Analyze what the user liked or disliked about the response. Consider:
            1. Communication style (empathetic vs clinical)
            2. Level of detail (brief vs comprehensive)
            3. Tone and approach
            4. Medical accuracy and helpfulness
            5. Specific issues mentioned in the comment

            Provide insights about user preferences in 2-3 sentences.
            """

            # For now, provide rule-based analysis (can be enhanced with actual LLM)
            analysis = self._rule_based_feedback_analysis(user_message, bot_response, feedback, feedback_comment)
            return analysis

        except Exception as e:
            print(f"LLM feedback analysis error: {e}")
            return f"Basic analysis: User gave {feedback} feedback"

    def _rule_based_feedback_analysis(self, user_message: str, bot_response: str, feedback: str, feedback_comment: str) -> str:
        """Rule-based feedback analysis as fallback"""
        analysis_parts = []

        # Analyze feedback type
        if feedback == "up":
            analysis_parts.append("User appreciated this response.")
        else:
            analysis_parts.append("User was dissatisfied with this response.")

        # Analyze comment for specific insights
        if feedback_comment:
            comment_lower = feedback_comment.lower()

            if any(word in comment_lower for word in ['too long', 'verbose', 'detailed']):
                analysis_parts.append("User prefers more concise responses.")
            elif any(word in comment_lower for word in ['too short', 'brief', 'more detail']):
                analysis_parts.append("User prefers more detailed explanations.")

            if any(word in comment_lower for word in ['cold', 'clinical', 'robotic']):
                analysis_parts.append("User prefers more empathetic communication.")
            elif any(word in comment_lower for word in ['too emotional', 'unprofessional']):
                analysis_parts.append("User prefers more clinical, professional tone.")

            if any(word in comment_lower for word in ['helpful', 'good', 'useful']):
                analysis_parts.append("User found the information valuable.")
            elif any(word in comment_lower for word in ['unhelpful', 'useless', 'wrong']):
                analysis_parts.append("User found the information inadequate.")

        # Analyze response characteristics
        response_length = len(bot_response.split())
        if response_length > 100:
            analysis_parts.append("Response was comprehensive and detailed.")
        elif response_length < 30:
            analysis_parts.append("Response was brief and concise.")

        empathy_words = ['understand', 'imagine', 'feel', 'concern', 'care']
        if any(word in bot_response.lower() for word in empathy_words):
            analysis_parts.append("Response included empathetic language.")

        return " ".join(analysis_parts)

    def _generate_improvement_suggestions(self, user_message: str, bot_response: str, feedback: str, feedback_comment: str) -> List[str]:
        """Generate specific improvement suggestions based on feedback"""
        suggestions = []

        if feedback == "down":
            # Analyze what went wrong and suggest improvements
            if feedback_comment:
                comment_lower = feedback_comment.lower()

                if any(word in comment_lower for word in ['too long', 'verbose']):
                    suggestions.append("Make responses more concise and to-the-point")
                elif any(word in comment_lower for word in ['too short', 'brief']):
                    suggestions.append("Provide more detailed explanations and background information")

                if any(word in comment_lower for word in ['cold', 'clinical', 'robotic']):
                    suggestions.append("Use more empathetic and caring language")
                elif any(word in comment_lower for word in ['unprofessional', 'too casual']):
                    suggestions.append("Maintain more professional medical tone")

                if any(word in comment_lower for word in ['confusing', 'unclear']):
                    suggestions.append("Improve clarity and structure of explanations")

                if any(word in comment_lower for word in ['wrong', 'inaccurate']):
                    suggestions.append("Verify medical information accuracy")

                if any(word in comment_lower for word in ['not helpful', 'useless']):
                    suggestions.append("Focus on more practical and actionable advice")

            # General suggestions for negative feedback
            if not suggestions:
                response_length = len(bot_response.split())
                if response_length > 150:
                    suggestions.append("Consider making responses more concise")
                elif response_length < 20:
                    suggestions.append("Consider providing more detailed information")

                empathy_words = ['understand', 'imagine', 'feel', 'concern', 'care']
                if not any(word in bot_response.lower() for word in empathy_words):
                    suggestions.append("Add more empathetic and understanding language")

        return suggestions

# Global feedback system instance
feedback_system = IntelligentFeedbackSystem()
