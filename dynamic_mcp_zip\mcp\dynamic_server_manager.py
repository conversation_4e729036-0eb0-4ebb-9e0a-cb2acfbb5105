"""
Dynamic MCP Server Manager
Creates and manages MCP servers dynamically based on user input
"""

import asyncio
import logging
import importlib
import tempfile
import os
import json
import aiohttp
from typing import Dict, Any, List, Optional
from pathlib import Path

logger = logging.getLogger(__name__)

class BaseDynamicServer:
    """Base class for all dynamic MCP servers"""
    def __init__(self, name: str, tool_name: str, tool_description: str):
        self.name = name
        self.tool_name = tool_name
        self.tool_description = tool_description
        self.is_active = False
        self.session = None
        
    async def initialize(self):
        """Initialize the server"""
        self.session = aiohttp.ClientSession()
        self.is_active = True
        logger.info(f"MCP Server {self.name} initialized successfully")
        return True
        
    async def cleanup(self):
        """Cleanup resources"""
        if self.session:
            await self.session.close()
            
    async def execute_tool(self, query: str, context: Dict[str, Any] = None) -> Dict[str, Any]:
        """Execute the custom tool with the given query"""
        raise NotImplementedError("Subclasses must implement execute_tool")
        
    def get_tool_info(self) -> Dict[str, Any]:
        """Get information about this tool"""
        return {
            "name": self.tool_name,
            "description": self.tool_description,
            "server_name": self.name,
            "active": self.is_active
        }

    def get_status(self) -> Dict[str, Any]:
        """Get server status"""
        return {
            "active": self.is_active,
            "tool_name": self.tool_name,
            "tool_description": self.tool_description
        }

class LLMBasedServer(BaseDynamicServer):
    """An LLM-based MCP server that uses language models for responses"""
    def __init__(self, name: str, tool_name: str, tool_description: str, 
                 llm_api_key: str, model_name: str):
        super().__init__(name, tool_name, tool_description)
        self.llm_api_key = llm_api_key
        self.model_name = model_name
        self.llm = None

    async def initialize(self):
        """Initialize the MCP server with LLM"""
        try:
            from langchain_groq import ChatGroq
            self.llm = ChatGroq(
                groq_api_key=self.llm_api_key,
                model_name=self.model_name,
                temperature=0.1,
                max_tokens=1000
            )
            await super().initialize()
            return True
        except Exception as e:
            logger.error(f"Failed to initialize LLM server {self.name}: {e}")
            return False

    async def execute_tool(self, query: str, context: Dict[str, Any] = None) -> Dict[str, Any]:
        """Execute the tool using LLM"""
        try:
            system_prompt = f"""You are a specialized tool called '{self.tool_name}'.
Tool Description: {self.tool_description}
IMPORTANT: You are an AI assistant and do NOT have access to real-time data or live APIs.
When asked for specific information:
1. Clearly state that you cannot access real-time information
2. Provide general guidance and suggestions
3. Recommend where users can find actual current information                
4. Be helpful but honest about your limitations
Context: {context or {}}
"""
            messages = [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": query}
            ]
            response = await self.llm.ainvoke(messages)
            return {
                "success": True,
                "response": response.content,
                "server_name": self.name,
                "disclaimer": "This response is generated by AI"
            }
        except Exception as e:
            logger.error(f"Error executing tool {self.tool_name}: {e}")
            return {"error": str(e)}

class GoogleSearchServer(BaseDynamicServer):
    """Google Search API Server"""
    def __init__(self, api_key: str, engine_id: str):
        super().__init__("Google Search Server", "google_search", 
                         "Real Google Custom Search API")
        self.api_key = api_key
        self.engine_id = engine_id

    async def execute_tool(self, query: str, context: Dict[str, Any] = None) -> Dict[str, Any]:
        """Execute Google search"""
        try:
            url = "https://www.googleapis.com/customsearch/v1"
            params = {
                "key": self.api_key,
                "cx": self.engine_id,
                "q": query,
                "num": 5
            }
            async with self.session.get(url, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    items = data.get("items", [])
                    
                    formatted_results = f"🔍 **Google Search Results for '{query}':**\n\n"
                    for i, item in enumerate(items[:3], 1):
                        title = item.get("title", "No title")
                        snippet = item.get("snippet", "No description")
                        link = item.get("link", "")
                        formatted_results += f"**{i}. {title}**\n{snippet}\n🔗 {link}\n\n"
                    formatted_results += f"*Found {len(items)} results*"
                    
                    return {
                        "success": True,
                        "response": formatted_results,
                        "real_data": True
                    }
                else:
                    error_text = await response.text()
                    return {"error": f"API error {response.status}: {error_text}"}
        except Exception as e:
            logger.error(f"Google Search failed: {e}")
            return {"error": str(e)}

class OpenWeatherServer(BaseDynamicServer):
    """OpenWeather API Server"""
    def __init__(self, api_key: str):
        super().__init__("OpenWeather Server", "weather_search", 
                         "Real weather data from OpenWeather API")
        self.api_key = api_key

    async def execute_tool(self, query: str, context: Dict[str, Any] = None) -> Dict[str, Any]:
        """Get weather data"""
        try:
            location = self._extract_location(query) or "London"
            url = "https://api.openweathermap.org/data/2.5/weather"
            params = {
                "q": location,
                "appid": self.api_key,
                "units": "metric"
            }
            async with self.session.get(url, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    city = data["name"]
                    country = data["sys"]["country"]
                    temp = data["main"]["temp"]
                    feels_like = data["main"]["feels_like"]
                    humidity = data["main"]["humidity"]
                    description = data["weather"][0]["description"].title()
                    
                    result = (
                        f"🌤️ **Weather in {city}, {country}:**\n\n"
                        f"**Temperature:** {temp}°C (feels like {feels_like}°C)\n"
                        f"**Condition:** {description}\n"
                        f"**Humidity:** {humidity}%\n\n"
                        "*Real-time data from OpenWeather API*"
                    )
                    return {
                        "success": True,
                        "response": result,
                        "real_data": True
                    }
                else:
                    error_text = await response.text()
                    return {"error": f"API error {response.status}: {error_text}"}
        except Exception as e:
            logger.error(f"Weather search failed: {e}")
            return {"error": str(e)}
            
    def _extract_location(self, query: str) -> str:
        """Extract location from query"""
        query_lower = query.lower()
        for trigger in [" in ", " for ", " at "]:
            if trigger in query_lower:
                return query.split(trigger)[-1].strip()
        return ""

class NewsAPIServer(BaseDynamicServer):
    """News API Server"""
    def __init__(self, api_key: str):
        super().__init__("News API Server", "news_search", 
                         "Real news articles from News API")
        self.api_key = api_key

    async def execute_tool(self, query: str, context: Dict[str, Any] = None) -> Dict[str, Any]:
        """Get news data"""
        try:
            url = "https://newsapi.org/v2/everything"
            headers = {"X-Api-Key": self.api_key}
            params = {"q": query, "pageSize": 5, "sortBy": "relevancy"}
            
            async with self.session.get(url, headers=headers, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    articles = data.get("articles", [])
                    
                    formatted_results = f"📰 **News about '{query}':**\n\n"
                    for i, article in enumerate(articles[:3], 1):
                        title = article.get("title", "No title")
                        source = article.get("source", {}).get("name", "Unknown")
                        url_link = article.get("url", "")
                        formatted_results += f"**{i}. {title}**\n*Source: {source}*\n🔗 {url_link}\n\n"
                    formatted_results += f"*Found {len(articles)} articles*"
                    
                    return {
                        "success": True,
                        "response": formatted_results,
                        "real_data": True
                    }
                else:
                    error_text = await response.text()
                    return {"error": f"API error {response.status}: {error_text}"}
        except Exception as e:
            logger.error(f"News search failed: {e}")
            return {"error": str(e)}

class VectorDBServer(BaseDynamicServer):
    """Vector Database Server"""
    def __init__(self, name: str, db_type: str, connection_string: str, 
                 collection_name: str, llm_api_key: str, model_name: str):
        super().__init__(name, "vector_search", 
                         f"Search {db_type} vector database")
        self.db_type = db_type
        self.connection_string = connection_string
        self.collection_name = collection_name
        self.llm_api_key = llm_api_key
        self.model_name = model_name

    async def execute_tool(self, query: str, context: Dict[str, Any] = None) -> Dict[str, Any]:
        """Search vector database"""
        try:
            # This would be implemented with actual vector DB client
            # For now, simulate a search result
            return {
                "success": True,
                "response": (
                    f"🔍 **Vector Search Results in {self.db_type}:**\n\n"
                    "1. Document about financial planning\n"
                    "2. Retirement investment strategies\n"
                    "3. Budgeting best practices\n\n"
                    f"*Searched collection: {self.collection_name}*"
                )
            }
        except Exception as e:
            logger.error(f"Vector search failed: {e}")
            return {"error": str(e)}

class SQLDBServer(BaseDynamicServer):
    """SQL Database Server"""
    def __init__(self, name: str, connection_string: str, 
                 table_name: str, llm_api_key: str, model_name: str):
        super().__init__(name, "sql_query", 
                         f"Query SQL database table: {table_name}")
        self.connection_string = connection_string
        self.table_name = table_name
        self.llm_api_key = llm_api_key
        self.model_name = model_name

    async def execute_tool(self, query: str, context: Dict[str, Any] = None) -> Dict[str, Any]:
        """Execute SQL query"""
        try:
            # This would be implemented with actual SQL client
            # For now, simulate query results
            return {
                "success": True,
                "response": (
                    f"📊 **SQL Query Results from {self.table_name}:**\n\n"
                    "| Account | Balance |\n"
                    "|---------|---------|\n"
                    "| Checking| $5,000 |\n"
                    "| Savings | $15,000 |\n"
                    "| IRA     | $45,000 |"
                )
            }
        except Exception as e:
            logger.error(f"SQL query failed: {e}")
            return {"error": str(e)}

class MongoDBServer(BaseDynamicServer):
    """MongoDB Server"""
    def __init__(self, name: str, connection_string: str, 
                 database_name: str, collection_name: str, 
                 llm_api_key: str, model_name: str):
        super().__init__(name, "mongo_query", 
                         f"Query MongoDB: {database_name}.{collection_name}")
        self.connection_string = connection_string
        self.database_name = database_name
        self.collection_name = collection_name
        self.llm_api_key = llm_api_key
        self.model_name = model_name

    async def execute_tool(self, query: str, context: Dict[str, Any] = None) -> Dict[str, Any]:
        """Query MongoDB"""
        try:
            # This would be implemented with actual MongoDB client
            return {
                "success": True,
                "response": (
                    f"🍃 **MongoDB Query Results from {self.database_name}.{self.collection_name}:**\n\n"
                    "- User profile: age=35, income=75000\n"
                    "- Transaction history: 12 records\n"
                    "- Investment preferences: moderate risk"
                )
            }
        except Exception as e:
            logger.error(f"MongoDB query failed: {e}")
            return {"error": str(e)}

class RedisServer(BaseDynamicServer):
    """Redis Server"""
    def __init__(self, name: str, connection_string: str, 
                 key_pattern: str, llm_api_key: str, model_name: str):
        super().__init__(name, "redis_cache", 
                         f"Access Redis cache with pattern: {key_pattern}")
        self.connection_string = connection_string
        self.key_pattern = key_pattern
        self.llm_api_key = llm_api_key
        self.model_name = model_name

    async def execute_tool(self, query: str, context: Dict[str, Any] = None) -> Dict[str, Any]:
        """Access Redis"""
        try:
            # This would be implemented with actual Redis client
            return {
                "success": True,
                "response": (
                    f"🔑 **Redis Cache Access with pattern '{self.key_pattern}':**\n\n"
                    "user:123:preferences = {'theme': 'dark', 'currency': 'USD'}\n"
                    "user:123:session = {'last_login': '2023-10-15'}"
                )
            }
        except Exception as e:
            logger.error(f"Redis access failed: {e}")
            return {"error": str(e)}

class CloudStorageServer(BaseDynamicServer):
    """Cloud Storage Server"""
    def __init__(self, name: str, cloud_type: str, credentials: Dict[str, str], 
                 file_path: str, llm_api_key: str, model_name: str):
        super().__init__(name, "cloud_storage", 
                         f"Access {cloud_type} storage at {file_path}")
        self.cloud_type = cloud_type
        self.credentials = credentials
        self.file_path = file_path
        self.llm_api_key = llm_api_key
        self.model_name = model_name

    async def execute_tool(self, query: str, context: Dict[str, Any] = None) -> Dict[str, Any]:
        """Access cloud storage"""
        try:
            # This would be implemented with actual cloud SDK
            return {
                "success": True,
                "response": (
                    f"☁️ **{self.cloud_type} Storage Access:**\n\n"
                    f"Accessed file: {self.file_path}\n"
                    "Content: Financial report Q3 2023\n"
                    "Size: 2.4 MB\n"
                    "Last modified: 2023-10-01"
                )
            }
        except Exception as e:
            logger.error(f"Cloud access failed: {e}")
            return {"error": str(e)}

class DynamicMCPServerManager:
    """Manages dynamic creation and execution of MCP servers"""
    
    def __init__(self):
        self.servers = {}
        self.next_port = 8010
        
    async def create_server(self, server_config: Dict[str, Any]) -> Dict[str, Any]:
        """Create a new MCP server dynamically"""
        try:
            server_name = server_config.get("server_name", f"dynamic_server_{len(self.servers)}")
            server_type = server_config.get("type", "llm")
            
            server_instance = None
            
            if server_type == "google_search":
                server_instance = GoogleSearchServer(
                    api_key=server_config["api_key"],
                    engine_id=server_config["engine_id"]
                )
            elif server_type == "openweather":
                server_instance = OpenWeatherServer(
                    api_key=server_config["api_key"]
                )
            elif server_type == "newsapi":
                server_instance = NewsAPIServer(
                    api_key=server_config["api_key"]
                )
            elif server_type == "vector_db":
                server_instance = VectorDBServer(
                    name=server_name,
                    db_type=server_config["db_type"],
                    connection_string=server_config["connection_string"],
                    collection_name=server_config["collection_name"],
                    llm_api_key=server_config["llm_api_key"],
                    model_name=server_config["model_name"]
                )
            elif server_type == "sql_db":
                server_instance = SQLDBServer(
                    name=server_name,
                    connection_string=server_config["connection_string"],
                    table_name=server_config["table_name"],
                    llm_api_key=server_config["llm_api_key"],
                    model_name=server_config["model_name"]
                )
            elif server_type == "mongo_db":
                server_instance = MongoDBServer(
                    name=server_name,
                    connection_string=server_config["connection_string"],
                    database_name=server_config["database_name"],
                    collection_name=server_config["collection_name"],
                    llm_api_key=server_config["llm_api_key"],
                    model_name=server_config["model_name"]
                )
            elif server_type == "redis":
                server_instance = RedisServer(
                    name=server_name,
                    connection_string=server_config["connection_string"],
                    key_pattern=server_config["key_pattern"],
                    llm_api_key=server_config["llm_api_key"],
                    model_name=server_config["model_name"]
                )
            elif server_type == "cloud_storage":
                server_instance = CloudStorageServer(
                    name=server_name,
                    cloud_type=server_config["cloud_type"],
                    credentials=server_config["credentials"],
                    file_path=server_config["file_path"],
                    llm_api_key=server_config["llm_api_key"],
                    model_name=server_config["model_name"]
                )
            else:  # Default to LLM-based server
                server_instance = LLMBasedServer(
                    name=server_name,
                    tool_name=server_config.get("tool_name", "custom_tool"),
                    tool_description=server_config["tool_description"],
                    llm_api_key=server_config["llm_api_key"],
                    model_name=server_config.get("model_name", "llama3-70b-8192")
                )
            
            # Initialize the server
            success = await server_instance.initialize()
            if not success:
                return {"success": False, "error": "Server initialization failed"}

            # Store server
            self.servers[server_name] = {
                "server": server_instance,
                "config": server_config,
                "port": self.next_port,
                "status": {
                    "active": True,
                    "tools_count": 1,
                    "created_at": asyncio.get_event_loop().time()
                }
            }
            self.next_port += 1

            logger.info(f"Created MCP server: {server_name}")
            return {
                "success": True,
                "server_name": server_name,
                "port": self.next_port - 1,
                "status": "running"
            }
                
        except Exception as e:
            logger.error(f"Failed to create dynamic server: {e}")
            return {"success": False, "error": str(e)}
    
    def get_servers(self) -> Dict[str, Any]:
        """Get all running servers"""
        return {
            name: {
                "status": info["server"].get_status(),
                "config": info["config"]
            }
            for name, info in self.servers.items()
        }
    
    async def stop_server(self, server_name: str) -> bool:
        """Stop and remove a specific server"""
        try:
            if server_name in self.servers:
                # Cleanup server resources
                server_instance = self.servers[server_name]["server"]
                await server_instance.cleanup()
                
                # Remove from servers dict
                del self.servers[server_name]

                logger.info(f"Stopped and removed MCP server: {server_name}")
                return True
            return False
        except Exception as e:
            logger.error(f"Failed to stop server {server_name}: {e}")
            return False

    async def execute_server_tool(self, server_name: str, query: str, context: Dict[str, Any] = None) -> Dict[str, Any]:
        """Execute a tool from a specific server"""
        try:
            if server_name not in self.servers:
                return {"error": f"Server {server_name} not found"}

            server_instance = self.servers[server_name]["server"]
            return await server_instance.execute_tool(query, context)

        except Exception as e:
            logger.error(f"Failed to execute tool on server {server_name}: {e}")
            return {"error": str(e)}

    def get_available_tools(self) -> List[Dict[str, Any]]:
        """Get list of all available tools from all servers"""
        tools = []
        for server_name, server_info in self.servers.items():
            server_instance = server_info["server"]
            tool_info = server_instance.get_tool_info()
            tools.append(tool_info)
        return tools

    def get_server_status(self) -> Dict[str, Any]:
        """Get status of all servers"""
        status = {}
        for server_name, server_info in self.servers.items():
            server_instance = server_info["server"]
            status[server_name] = {
                "status": "active" if server_instance.is_active else "inactive",
                "tools_count": 1,  # Each server has one tool
                "tool_name": server_instance.tool_name,
                "tool_description": server_instance.tool_description
            }
        return status

    async def test_tool(self, tool_name: str, test_query: str = None) -> Dict[str, Any]:
        """Test an MCP tool with a sample query"""
        try:
            if tool_name not in self.servers:
                available_tools = list(self.servers.keys())
                return {"success": False, "error": f"Tool '{tool_name}' not found. Available tools: {available_tools}"}

            server_info = self.servers[tool_name]
            server_instance = server_info["server"]

            if not server_instance.is_active:
                return {"success": False, "error": f"Tool '{tool_name}' is not active"}

            # Use provided test query or generate one
            if not test_query:
                test_query = self._get_default_test_query(server_instance.tool_name)

            # Execute the tool
            result = await server_instance.execute_tool(test_query, {})

            return {
                "success": True,
                "tool_name": tool_name,
                "test_query": test_query,
                "result": result,
                "tool_description": server_instance.tool_description
            }

        except Exception as e:
            logger.error(f"Tool testing failed: {e}")
            return {"success": False, "error": str(e)}

    def _get_default_test_query(self, tool_name: str) -> str:
        """Generate default test queries based on tool name"""
        tool_lower = tool_name.lower()
        if "search" in tool_lower:
            return "current financial news"
        elif "weather" in tool_lower:
            return "weather in New York"
        elif "vector" in tool_lower:
            return "retirement planning advice"
        elif "sql" in tool_lower:
            return "show account balances"
        elif "mongo" in tool_lower:
            return "find user profiles"
        elif "redis" in tool_lower:
            return "get user preferences"
        elif "cloud" in tool_lower:
            return "retrieve financial report"
        else:
            return "test query"

    async def validate_all_servers(self) -> Dict[str, Any]:
        """Validate all MCP servers and their functionality"""
        validation_results = {}
        for server_name, server_info in self.servers.items():
            try:
                server_instance = server_info["server"]
                validation = {
                    "active": server_instance.is_active,
                    "tool_name": server_instance.tool_name,
                    "health_score": 100 if server_instance.is_active else 0,
                    "status": "healthy" if server_instance.is_active else "inactive"
                }
                validation_results[server_name] = validation
            except Exception as e:
                validation_results[server_name] = {
                    "error": str(e),
                    "health_score": 0,
                    "status": "error"
                }
        return validation_results

# Global manager instance
dynamic_mcp_manager = DynamicMCPServerManager()