from typing import Dict
from fastmcp import FastMCP
from qdrant_client import Qdrant<PERSON>lient
from qdrant_client.models import PointStruct, VectorParams, Distance
from sentence_transformers import SentenceTransformer
import uuid
import os
import logging
from dotenv import load_dotenv

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

load_dotenv()
mcp = FastMCP("SemanticMemory")
QDRANT_API_KEY = os.getenv("Qdrant_API_KEY")

# Connect to Qdrant cloud instance
QDRANT_URL = "https://c4c84cb4-8c10-42a6-8b30-6b51c73c8757.us-west-1-0.aws.cloud.qdrant.io:6333"

# Initialize components with error handling
def initialize_components():
    """Initialize Qdrant client and sentence transformer with proper error handling."""
    global qdrant_client, encoder

    try:
        # Initialize Qdrant client
        qdrant_client = QdrantClient(
            url=QDRANT_URL,
            api_key=QDRANT_API_KEY,
        )
        logger.info("Successfully connected to Qdrant")

        # Initialize sentence transformer with error handling
        try:
            # Try to load the model without authentication first
            os.environ.pop('HF_TOKEN', None)  # Remove any existing token
            encoder = SentenceTransformer("all-MiniLM-L6-v2", token=None)
            logger.info("Successfully loaded sentence transformer")
        except Exception as e:
            logger.warning(f"Failed to load sentence transformer: {e}")
            logger.info("Trying alternative model...")
            # Fallback to a different model or local model
            try:
                encoder = SentenceTransformer("paraphrase-MiniLM-L6-v2", token=None)
                logger.info("Successfully loaded fallback sentence transformer")
            except Exception as e2:
                logger.error(f"Failed to load any sentence transformer: {e2}")
                raise Exception("Could not initialize sentence transformer. Please check your Hugging Face setup.")

    except Exception as e:
        logger.error(f"Error initializing components: {e}")
        raise

# Initialize components
try:
    initialize_components()
except Exception as e:
    logger.error(f"Failed to initialize semantic memory system: {e}")
    # Set fallback values
    qdrant_client = None
    encoder = None

COLLECTION_NAME = "semantic_memory"

# Ensure collection exists
try:
    if not qdrant_client.collection_exists(COLLECTION_NAME):
        qdrant_client.recreate_collection(
            collection_name=COLLECTION_NAME,
            vectors_config=VectorParams(size=384, distance=Distance.COSINE)
        )
        print(f"Created collection: {COLLECTION_NAME}")
except Exception as e:
    print(f"Error connecting to Qdrant: {e}")

@mcp.tool()
async def add_semantic_memory(patient_name: str, content: str, category: str = "general") -> Dict[str, str]:
    """
    Stores a semantic fact about a patient.
    Compatible with MCP Client Adapter.
    """
    if not qdrant_client or not encoder:
        return {
            "success": False,
            "status": "error",
            "memory_type": "semantic",
            "error": "Semantic memory system not initialized"
        }

    try:
        # Create enriched content with category
        enriched_content = f"[{category}] {content}"
        embedding = encoder.encode(enriched_content).tolist()
        point = PointStruct(
            id=str(uuid.uuid4()),
            vector=embedding,
            payload={
                "patient_name": patient_name,
                "content": content,
                "category": category,
                "enriched_content": enriched_content
            }
        )
        qdrant_client.upsert(collection_name=COLLECTION_NAME, points=[point])
        logger.info(f"Stored semantic memory for patient {patient_name}: {content[:50]}...")
        return {
            "success": True,
            "status": "stored",
            "memory_type": "semantic",
            "content": content,
            "category": category
        }
    except Exception as e:
        logger.error(f"Error storing semantic memory: {e}")
        return {
            "success": False,
            "status": "error",
            "memory_type": "semantic",
            "error": str(e)
        }

# Alias for MCP Client Adapter compatibility
@mcp.tool()
async def semantic_add_memory(patient_name: str, content: str, category: str = "general") -> Dict[str, str]:
    """
    Alias for add_semantic_memory - used by MCP Client Adapter
    """
    return await add_semantic_memory(patient_name, content, category)

@mcp.tool()
async def get_semantic_memory(patient_name: str, context: str = "") -> Dict[str, str]:
    """
    Retrieves relevant semantic memory chunks for a patient using similarity search.
    Compatible with MCP Client Adapter.

    Args:
        patient_name: The name of the patient
        context: Optional context to guide the search (e.g., symptoms or current topic)
    """
    if not qdrant_client or not encoder:
        return {
            "memory_type": "semantic",
            "content": "Semantic memory system not initialized"
        }

    try:
        # Use context if provided, otherwise use default query
        query_text = context if context else f"health information about patient {patient_name}"
        query_vector = encoder.encode(query_text).tolist()

        result = qdrant_client.search(
            collection_name=COLLECTION_NAME,
            query_vector=query_vector,
            limit=5,
            query_filter={
                "must": [{"key": "patient_name", "match": {"value": patient_name}}]
            }
        )

        if result:
            # Group by category for better organization
            facts_by_category = {}
            for hit in result:
                category = hit.payload.get("category", "general")
                content = hit.payload.get("content", hit.payload.get("fact", ""))
                if category not in facts_by_category:
                    facts_by_category[category] = []
                facts_by_category[category].append(content)

            # Format output
            formatted_facts = []
            for category, facts in facts_by_category.items():
                formatted_facts.append(f"{category.title()}: {'; '.join(facts)}")

            content = "\n".join(formatted_facts)
        else:
            content = "No semantic memory found."

        logger.info(f"Retrieved {len(result)} semantic memories for patient {patient_name}")
        return {
            "memory_type": "semantic",
            "content": content
        }
    except Exception as e:
        logger.error(f"Error retrieving semantic memory: {e}")
        return {
            "memory_type": "semantic",
            "content": f"Error retrieving semantic memory: {str(e)}"
        }

if __name__ == "__main__":
    mcp.run(transport="stdio", port=8003)
