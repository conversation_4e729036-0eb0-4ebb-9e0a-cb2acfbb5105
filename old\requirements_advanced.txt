# Core dependencies (existing)
streamlit>=1.28.0
asyncio-mqtt>=0.11.0
pymongo>=4.5.0
python-dotenv>=1.0.0
qdrant-client>=1.6.0
sentence-transformers>=2.2.2

# Advanced Guardrails Dependencies
presidio-analyzer>=2.2.33
presidio-anonymizer>=2.2.33
detoxify>=0.5.2
torch>=2.0.0
spacy>=3.7.0

# NLP and ML Dependencies
transformers>=4.30.0
numpy>=1.24.0
pandas>=2.0.0
scikit-learn>=1.3.0

# Additional utilities
requests>=2.31.0
aiohttp>=3.8.0
pydantic>=2.0.0

# For spaCy model download (run after installation)
# python -m spacy download en_core_web_sm

# Installation commands:
# pip install -r requirements_advanced.txt
# python -m spacy download en_core_web_sm
