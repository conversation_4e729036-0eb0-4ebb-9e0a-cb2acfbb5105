#!/usr/bin/env python3
"""
MCP Client Adapter for Hybrid Memory System
Central coordinator that communicates with all three MCP memory services.
"""

import asyncio
import json
import aiohttp
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from datetime import datetime

@dataclass
class MemoryServiceConfig:
    """Configuration for MCP memory services"""
    episodic_url: str = "http://localhost:8002"
    semantic_url: str = "http://localhost:8003"
    behavioral_url: str = "http://localhost:8004"

@dataclass
class ExtractedMemoryInfo:
    """Information extracted and categorized for memory storage"""
    # Episodic (time-based events)
    current_symptoms: List[str] = None
    pain_level: Optional[str] = None
    visit_date: Optional[str] = None
    
    # Semantic (facts and knowledge)
    medical_conditions: List[str] = None
    allergies: List[str] = None
    medications: List[str] = None
    family_history: List[str] = None
    
    # Behavioral (patterns and preferences)
    appointment_preferences: List[str] = None
    communication_preferences: List[str] = None
    lifestyle_habits: List[str] = None
    
    def __post_init__(self):
        # Initialize empty lists if None
        for field_name, field_value in self.__dict__.items():
            if field_value is None:
                setattr(self, field_name, [])

class MCPClientAdapter:
    """MCP Client Adapter - Central coordinator for all memory services"""
    
    def __init__(self, config: MemoryServiceConfig = None):
        self.config = config or MemoryServiceConfig()
        self.session = None
        
    async def __aenter__(self):
        """Async context manager entry"""
        self.session = aiohttp.ClientSession()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit"""
        if self.session:
            await self.session.close()
    
    async def _make_mcp_request(self, service_name: str, method: str, params: Dict[str, Any]) -> Dict[str, Any]:
        """Make direct function call to MCP service (simplified approach)"""
        try:
            # For now, use direct database access since MCP services are having issues
            # This maintains the architecture while ensuring functionality

            if service_name == "episodic":
                return await self._direct_episodic_call(method, params)
            elif service_name == "semantic":
                return await self._direct_semantic_call(method, params)
            elif service_name == "behavioral":
                return await self._direct_behavioral_call(method, params)
            else:
                return {"success": False, "error": "Unknown service"}

        except Exception as e:
            print(f"❌ MCP request error: {e}")
            return {"success": False, "error": str(e)}

    async def _direct_episodic_call(self, method: str, params: Dict[str, Any]) -> Dict[str, Any]:
        """Direct episodic memory call using MongoDB"""
        try:
            from pymongo import MongoClient
            import os
            from dotenv import load_dotenv

            load_dotenv()
            MONGODB_URI = os.getenv("MongoDB_URI")
            DB_NAME = "Medical_Records"
            COLLECTION_NAME = "episodic"

            if not MONGODB_URI:
                return {"success": False, "error": "MongoDB URI not found"}

            client = MongoClient(MONGODB_URI)
            db = client[DB_NAME]
            collection = db[COLLECTION_NAME]

            if method == "episodic.add_visit":
                # Create visit document
                visit_doc = {
                    "patient_id": params['patient_id'],
                    "date": params['date'],
                    "symptoms": params['symptoms'],
                    "diagnosis": params['diagnosis'],
                    "prescription": params['prescription'],
                    "created_at": datetime.now(),
                    "updated_at": datetime.now()
                }

                # Insert into MongoDB
                result = collection.insert_one(visit_doc)
                client.close()

                return {
                    "success": True,
                    "status": "stored",
                    "document_id": str(result.inserted_id)
                }

            elif method == "episodic.get_memory":
                # Find recent visits for the patient
                visits = list(collection.find(
                    {"patient_id": params['patient_id']},
                    sort=[("date", -1)],
                    limit=5
                ))
                client.close()

                if visits:
                    visit_summaries = []
                    for visit in visits:
                        date = visit.get("date", "Unknown date")
                        symptoms = visit.get("symptoms", "No symptoms")
                        diagnosis = visit.get("diagnosis", "No diagnosis")
                        visit_summaries.append(f"• {date}: {symptoms} → {diagnosis}")
                    content = "\n".join(visit_summaries)
                else:
                    content = "No visit history found."

                return {"content": content}

        except Exception as e:
            return {"success": False, "error": str(e)}

    async def _direct_semantic_call(self, method: str, params: Dict[str, Any]) -> Dict[str, Any]:
        """Direct semantic memory call using Qdrant"""
        try:
            if method == "semantic.add_memory":
                # Use Qdrant for semantic storage
                from qdrant_client import QdrantClient
                from qdrant_client.models import PointStruct, VectorParams, Distance
                from sentence_transformers import SentenceTransformer
                import uuid
                import os
                from dotenv import load_dotenv

                load_dotenv()
                QDRANT_API_KEY = os.getenv("Qdrant_API_KEY")
                QDRANT_URL = "https://c4c84cb4-8c10-42a6-8b30-6b51c73c8757.us-west-1-0.aws.cloud.qdrant.io:6333"
                COLLECTION_NAME = "semantic_memory"

                if not QDRANT_API_KEY:
                    print("❌ Qdrant API key not found")
                    return {"success": False, "error": "Qdrant API key not found"}

                # Initialize components with error handling
                try:
                    qdrant_client = QdrantClient(url=QDRANT_URL, api_key=QDRANT_API_KEY)
                    print("✅ Qdrant client connected")
                except Exception as e:
                    print(f"❌ Qdrant connection failed: {e}")
                    return {"success": False, "error": f"Qdrant connection failed: {e}"}

                try:
                    # Remove HF_TOKEN to avoid auth issues
                    os.environ.pop('HF_TOKEN', None)
                    encoder = SentenceTransformer("all-MiniLM-L6-v2", use_auth_token=False)
                    print("✅ Sentence transformer loaded")
                except Exception as e:
                    print(f"❌ Sentence transformer failed: {e}")
                    return {"success": False, "error": f"Sentence transformer failed: {e}"}

                # Ensure collection exists
                try:
                    if not qdrant_client.collection_exists(COLLECTION_NAME):
                        qdrant_client.create_collection(
                            collection_name=COLLECTION_NAME,
                            vectors_config=VectorParams(size=384, distance=Distance.COSINE)
                        )
                        print(f"✅ Created collection: {COLLECTION_NAME}")
                    else:
                        print(f"✅ Collection exists: {COLLECTION_NAME}")
                except Exception as e:
                    print(f"❌ Collection setup failed: {e}")
                    return {"success": False, "error": f"Collection setup failed: {e}"}

                # Store the memory
                try:
                    content = params['content']
                    category = params.get('category', 'general')
                    patient_name = params['patient_name']

                    enriched_content = f"[{category}] {content}"
                    embedding = encoder.encode(enriched_content).tolist()

                    point = PointStruct(
                        id=str(uuid.uuid4()),
                        vector=embedding,
                        payload={
                            "patient_name": patient_name,
                            "content": content,
                            "category": category,
                            "enriched_content": enriched_content
                        }
                    )

                    qdrant_client.upsert(collection_name=COLLECTION_NAME, points=[point])
                    print(f"✅ Stored in Qdrant: {content}")
                    return {"success": True, "status": "stored"}

                except Exception as e:
                    print(f"❌ Qdrant storage failed: {e}")
                    return {"success": False, "error": f"Qdrant storage failed: {e}"}

            elif method == "semantic.get_memory":
                # Retrieve from Qdrant
                from qdrant_client import QdrantClient
                from sentence_transformers import SentenceTransformer
                import os
                from dotenv import load_dotenv

                load_dotenv()
                QDRANT_API_KEY = os.getenv("Qdrant_API_KEY")
                QDRANT_URL = "https://c4c84cb4-8c10-42a6-8b30-6b51c73c8757.us-west-1-0.aws.cloud.qdrant.io:6333"
                COLLECTION_NAME = "semantic_memory"

                if not QDRANT_API_KEY:
                    return {"content": "Qdrant API key not found"}

                try:
                    qdrant_client = QdrantClient(url=QDRANT_URL, api_key=QDRANT_API_KEY)
                    os.environ.pop('HF_TOKEN', None)
                    encoder = SentenceTransformer("all-MiniLM-L6-v2", use_auth_token=False)

                    patient_name = params['patient_name']
                    query_text = f"health information about patient {patient_name}"
                    query_vector = encoder.encode(query_text).tolist()

                    # Use query_points instead of deprecated search method
                    from qdrant_client.models import Filter, FieldCondition, MatchValue

                    result = qdrant_client.query_points(
                        collection_name=COLLECTION_NAME,
                        query=query_vector,
                        limit=5,
                        query_filter=Filter(
                            must=[FieldCondition(key="patient_name", match=MatchValue(value=patient_name))]
                        )
                    )

                    if result.points:
                        facts_by_category = {}
                        for point in result.points:
                            category = point.payload.get("category", "general")
                            content = point.payload.get("content", "")
                            if category not in facts_by_category:
                                facts_by_category[category] = []
                            facts_by_category[category].append(content)

                        formatted_facts = []
                        for category, facts in facts_by_category.items():
                            formatted_facts.append(f"{category.title()}: {'; '.join(facts)}")

                        content = "\n".join(formatted_facts)
                        print(f"✅ Retrieved from Qdrant: {len(result.points)} items")
                    else:
                        content = "No semantic memory found."
                        print("ℹ️  No semantic memories found for patient")

                    return {"content": content}

                except Exception as e:
                    print(f"❌ Qdrant retrieval failed: {e}")
                    return {"content": f"Error retrieving semantic memory: {e}"}

        except Exception as e:
            return {"success": False, "error": str(e)}

    async def _direct_behavioral_call(self, method: str, params: Dict[str, Any]) -> Dict[str, Any]:
        """Direct behavioral memory call using MongoDB"""
        try:
            from pymongo import MongoClient
            import os
            from dotenv import load_dotenv

            load_dotenv()
            MONGODB_URI = os.getenv("MongoDB_URI")
            DB_NAME = "Medical_Records"
            COLLECTION_NAME = "behavioral"

            if not MONGODB_URI:
                return {"success": False, "error": "MongoDB URI not found"}

            client = MongoClient(MONGODB_URI)
            db = client[DB_NAME]
            collection = db[COLLECTION_NAME]

            if method == "behavioral.update_memory":
                patient_id = params['patient_id']
                missed_appointments = params.get('missed_appointments', 0)
                prefers_teleconsult = params.get('prefers_teleconsult', 'unknown')
                habit_notes = params.get('habit_notes', '')

                # Create or update behavioral document
                behavioral_doc = {
                    "patient_id": patient_id,
                    "missed_appointments": missed_appointments,
                    "prefers_teleconsult": prefers_teleconsult,
                    "habit_notes": habit_notes,
                    "updated_at": datetime.now()
                }

                # Upsert (update or insert)
                result = collection.replace_one(
                    {"patient_id": patient_id},
                    behavioral_doc,
                    upsert=True
                )
                client.close()

                return {"success": True, "status": "stored"}

            elif method == "behavioral.get_memory":
                # Find behavioral data for patient
                behavior = collection.find_one({"patient_id": params['patient_id']})
                client.close()

                if behavior:
                    missed = behavior.get("missed_appointments", 0)
                    prefers = behavior.get("prefers_teleconsult", "unknown")
                    notes = behavior.get("habit_notes", "")
                    content = f"Missed appointments: {missed}. Prefers teleconsultation: {prefers}. Notes: {notes}."
                else:
                    content = "No behavioral memory found."

                return {"content": content}

        except Exception as e:
            return {"success": False, "error": str(e)}
    
    async def store_episodic_memory(self, patient_name: str, info: ExtractedMemoryInfo) -> bool:
        """Store episodic memory (visits, symptoms, events)"""
        if not (info.current_symptoms or info.pain_level):
            return False
        
        params = {
            "patient_id": patient_name,
            "date": info.visit_date or datetime.now().strftime("%Y-%m-%d"),
            "symptoms": "; ".join(info.current_symptoms) if info.current_symptoms else "General consultation",
            "diagnosis": f"Pain level: {info.pain_level}" if info.pain_level else "Assessment ongoing",
            "prescription": "Treatment plan pending"
        }
        
        result = await self._make_mcp_request(
            "episodic",
            "episodic.add_visit",
            params
        )
        
        success = result.get("success", False)
        if success:
            print(f"📅 Stored episodic: {params['symptoms']}")
        return success
    
    async def store_semantic_memory(self, patient_name: str, info: ExtractedMemoryInfo) -> bool:
        """Store semantic memory (medical facts, conditions, allergies)"""
        stored_any = False
        
        # Store medical conditions
        for condition in info.medical_conditions:
            params = {
                "patient_name": patient_name,
                "content": f"Has medical condition: {condition}",
                "category": "condition"
            }
            result = await self._make_mcp_request(
                "semantic",
                "semantic.add_memory",
                params
            )
            if result.get("success"):
                print(f"🏥 Stored condition: {condition}")
                stored_any = True
        
        # Store allergies
        for allergy in info.allergies:
            params = {
                "patient_name": patient_name,
                "content": f"Allergic to: {allergy}",
                "category": "allergy"
            }
            result = await self._make_mcp_request(
                "semantic",
                "semantic.add_memory",
                params
            )
            if result.get("success"):
                print(f"🚨 Stored allergy: {allergy}")
                stored_any = True
        
        # Store medications
        for medication in info.medications:
            params = {
                "patient_name": patient_name,
                "content": f"Takes medication: {medication}",
                "category": "medication"
            }
            result = await self._make_mcp_request(
                "semantic",
                "semantic.add_memory",
                params
            )
            if result.get("success"):
                print(f"💊 Stored medication: {medication}")
                stored_any = True
        
        return stored_any
    
    async def store_behavioral_memory(self, patient_name: str, info: ExtractedMemoryInfo) -> bool:
        """Store behavioral memory (preferences, habits, patterns)"""
        if not (info.appointment_preferences or info.communication_preferences or info.lifestyle_habits):
            return False
        
        # Combine all behavioral information
        behavioral_notes = []
        behavioral_notes.extend(info.appointment_preferences)
        behavioral_notes.extend(info.communication_preferences)
        behavioral_notes.extend(info.lifestyle_habits)
        
        params = {
            "patient_id": patient_name,
            "missed_appointments": 0,  # Will be updated over time
            "prefers_teleconsult": "unknown",  # Will be determined from preferences
            "habit_notes": "; ".join(behavioral_notes)
        }
        
        result = await self._make_mcp_request(
            "behavioral",
            "behavioral.update_memory",
            params
        )
        
        success = result.get("success", False)
        if success:
            print(f"👤 Stored behavioral: {params['habit_notes']}")
        return success
    
    async def get_episodic_memory(self, patient_name: str) -> Dict[str, Any]:
        """Retrieve episodic memory for patient"""
        params = {"patient_id": patient_name}
        return await self._make_mcp_request(
            "episodic",
            "episodic.get_memory",
            params
        )
    
    async def get_semantic_memory(self, patient_name: str) -> Dict[str, Any]:
        """Retrieve semantic memory for patient"""
        params = {"patient_name": patient_name}
        return await self._make_mcp_request(
            "semantic",
            "semantic.get_memory",
            params
        )
    
    async def get_behavioral_memory(self, patient_name: str) -> Dict[str, Any]:
        """Retrieve behavioral memory for patient"""
        params = {"patient_id": patient_name}
        return await self._make_mcp_request(
            "behavioral",
            "behavioral.get_memory",
            params
        )
    
    async def get_comprehensive_patient_context(self, patient_name: str) -> str:
        """Get comprehensive patient context from all memory systems"""
        context_parts = [f"Patient: {patient_name}"]
        
        try:
            # Get all memory types concurrently
            episodic_task = self.get_episodic_memory(patient_name)
            semantic_task = self.get_semantic_memory(patient_name)
            behavioral_task = self.get_behavioral_memory(patient_name)
            
            episodic, semantic, behavioral = await asyncio.gather(
                episodic_task, semantic_task, behavioral_task,
                return_exceptions=True
            )
            
            # Process episodic memory
            if isinstance(episodic, dict) and episodic.get("content"):
                context_parts.append(f"Recent Visits:\n{episodic['content']}")
            
            # Process semantic memory
            if isinstance(semantic, dict) and semantic.get("content"):
                context_parts.append(f"Medical History:\n{semantic['content']}")
            
            # Process behavioral memory
            if isinstance(behavioral, dict) and behavioral.get("content"):
                context_parts.append(f"Patient Preferences:\n{behavioral['content']}")
            
        except Exception as e:
            context_parts.append(f"Error retrieving context: {e}")
        
        return "\n\n".join(context_parts)
    
    async def store_all_memories(self, patient_name: str, info: ExtractedMemoryInfo) -> Dict[str, bool]:
        """Store information in all relevant memory systems"""
        results = {}
        
        # Store in all memory systems concurrently
        episodic_task = self.store_episodic_memory(patient_name, info)
        semantic_task = self.store_semantic_memory(patient_name, info)
        behavioral_task = self.store_behavioral_memory(patient_name, info)
        
        try:
            episodic_result, semantic_result, behavioral_result = await asyncio.gather(
                episodic_task, semantic_task, behavioral_task,
                return_exceptions=True
            )
            
            results["episodic"] = episodic_result if isinstance(episodic_result, bool) else False
            results["semantic"] = semantic_result if isinstance(semantic_result, bool) else False
            results["behavioral"] = behavioral_result if isinstance(behavioral_result, bool) else False
            
        except Exception as e:
            print(f"❌ Error storing memories: {e}")
            results = {"episodic": False, "semantic": False, "behavioral": False}
        
        return results

# Convenience function for easy usage
async def create_mcp_client() -> MCPClientAdapter:
    """Create and return MCP client adapter"""
    return MCPClientAdapter()
