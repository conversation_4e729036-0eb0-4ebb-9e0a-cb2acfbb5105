from typing import Dict
from fastmcp import FastMCP
from pymongo import MongoClient
import os
from dotenv import load_dotenv
from datetime import datetime

load_dotenv()
mcp = FastMCP("BehavioralMemory")

# MongoDB configuration
MONGODB_URI = os.getenv("MongoDB_URI")
DB_NAME = "Medical_Records"
COLLECTION_NAME = "behavioral"

def get_mongodb_collection():
    """Get MongoDB collection"""
    client = MongoClient(MONGODB_URI)
    db = client[DB_NAME]
    return db[COLLECTION_NAME], client

@mcp.tool()
async def get_behavioral_memory(patient_id: str) -> Dict[str, str]:
    """
    Returns behavioral memory for a patient from MongoDB.
    """
    try:
        collection, client = get_mongodb_collection()

        # Find behavioral data for patient
        behavior = collection.find_one({"patient_id": patient_id})
        client.close()

        if behavior:
            missed = behavior.get("missed_appointments", 0)
            prefers = behavior.get("prefers_teleconsult", "unknown")
            notes = behavior.get("habit_notes", "")
            content = (
                f"Missed appointments: {missed}. "
                f"Prefers teleconsultation: {prefers}. "
                f"Notes: {notes}."
            )
            return {"memory_type": "behavioral", "content": content}
        else:
            return {"memory_type": "behavioral", "content": "No behavioral memory found."}
    except Exception as e:
        return {"memory_type": "behavioral", "content": f"Error retrieving behavioral memory: {str(e)}"}

@mcp.tool()
async def update_behavioral_memory(
    patient_id: str,
    missed_appointments: int,
    prefers_teleconsult: str,
    habit_notes: str
) -> Dict[str, str]:
    """
    Updates or inserts behavioral memory for a patient in MongoDB.
    Compatible with MCP Client Adapter.
    """
    try:
        collection, client = get_mongodb_collection()

        # Create or update behavioral document
        behavioral_doc = {
            "patient_id": patient_id,
            "missed_appointments": missed_appointments,
            "prefers_teleconsult": prefers_teleconsult,
            "habit_notes": habit_notes,
            "updated_at": datetime.now()
        }

        # Check if document exists
        existing = collection.find_one({"patient_id": patient_id})

        if existing:
            # Update existing document
            collection.replace_one({"patient_id": patient_id}, behavioral_doc)
            action = "updated"
        else:
            # Insert new document
            behavioral_doc["created_at"] = datetime.now()
            collection.insert_one(behavioral_doc)
            action = "created"

        client.close()

        return {
            "success": True,
            "status": action,
            "memory_type": "behavioral",
            "patient_id": patient_id
        }
    except Exception as e:
        return {
            "success": False,
            "status": "error",
            "memory_type": "behavioral",
            "error": str(e)
        }

# Alias for MCP Client Adapter compatibility
@mcp.tool()
async def behavioral_update_memory(
    patient_id: str,
    missed_appointments: int,
    prefers_teleconsult: str,
    habit_notes: str
) -> Dict[str, str]:
    """
    Alias for update_behavioral_memory - used by MCP Client Adapter
    """
    return await update_behavioral_memory(patient_id, missed_appointments, prefers_teleconsult, habit_notes)

if __name__ == "__main__":
    mcp.run(transport="stdio", port=8004)
