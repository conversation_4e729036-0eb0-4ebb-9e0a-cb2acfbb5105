#!/usr/bin/env python3
"""
Comprehensive test script for the advanced medical chatbot system
Tests guardrails, feedback system, and UI improvements
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_input_guardrails():
    """Test advanced input guardrails"""
    print("🛡️ Testing Advanced Input Guardrails")
    print("=" * 50)
    
    try:
        from input_guardrail.advanced_input_guard import advanced_input_guard
        
        test_cases = [
            ("Normal medical query", "I have a headache and fever"),
            ("Emergency detection", "I'm having severe chest pain and can't breathe"),
            ("PII detection", "My name is <PERSON> and my SSN is ***********"),
            ("Toxic content", "You're a terrible doctor and I hate you"),
            ("High-risk content", "I want to kill myself"),
            ("Sensitive medical", "I think I have HIV and I'm scared")
        ]
        
        for test_name, test_input in test_cases:
            print(f"\n🧪 {test_name}:")
            print(f"   Input: '{test_input}'")
            
            result = advanced_input_guard.analyze_input(test_input)
            
            print(f"   ✅ Safe: {result.is_safe}")
            print(f"   🚨 Risk Level: {result.risk_level}")
            print(f"   🔍 Issues: {result.detected_issues}")
            print(f"   🚑 Emergency: {result.medical_emergency}")
            print(f"   🔒 Action: {result.action_required}")
            print(f"   📊 Confidence: {result.confidence_score:.2f}")
            
            if result.pii_detected:
                print(f"   🔐 PII Found: {result.pii_detected}")
                print(f"   🧹 Sanitized: '{result.sanitized_input}'")
        
        print("\n✅ Input guardrails test completed!")
        return True
        
    except Exception as e:
        print(f"❌ Input guardrails test failed: {e}")
        return False

def test_response_guardrails():
    """Test advanced response guardrails"""
    print("\n🛡️ Testing Advanced Response Guardrails")
    print("=" * 50)
    
    try:
        from response_guardrail.advanced_response_guard import advanced_response_guard
        
        test_responses = [
            ("Compliant response", "I understand your concern about headaches. While I can provide general information, please consult a healthcare provider for proper evaluation."),
            ("Prohibited advice", "I can diagnose you with migraines. Stop taking your current medication and increase the dose of ibuprofen."),
            ("Missing disclaimer", "You have diabetes. Take metformin 500mg twice daily."),
            ("Toxic response", "You're being stupid about your health and should just deal with it."),
            ("Good empathetic response", "I understand this must be concerning for you. Headaches can be uncomfortable, but there are ways to manage them.")
        ]
        
        for test_name, test_response in test_responses:
            print(f"\n🧪 {test_name}:")
            print(f"   Response: '{test_response[:80]}...'")
            
            result = advanced_response_guard.validate_response(test_response)
            
            print(f"   ✅ Compliant: {result.is_compliant}")
            print(f"   🚨 Risk Level: {result.risk_level}")
            print(f"   🔍 Issues: {result.detected_issues}")
            print(f"   💊 Medical Compliance: {result.medical_compliance}")
            print(f"   🚫 Inappropriate Advice: {result.inappropriate_advice}")
            print(f"   📊 Confidence: {result.confidence_score:.2f}")
            print(f"   🎯 Action: {result.action_taken}")
            
            if result.enhanced_response != test_response:
                print(f"   ✨ Enhanced: Yes")
                print(f"   📝 Disclaimers: {result.disclaimers_added}")
        
        print("\n✅ Response guardrails test completed!")
        return True
        
    except Exception as e:
        print(f"❌ Response guardrails test failed: {e}")
        return False

def test_unified_guardrails():
    """Test unified guardrail system"""
    print("\n🛡️ Testing Unified Guardrail System")
    print("=" * 50)
    
    try:
        from unified_guardrail.unified_guard_system import unified_guard_system
        
        test_conversations = [
            {
                "user_input": "I have a headache",
                "llm_response": "Headaches can be caused by stress, dehydration, or other factors. Rest and hydration may help."
            },
            {
                "user_input": "I'm having chest pain",
                "llm_response": "Chest pain can be serious. You should see a doctor immediately."
            },
            {
                "user_input": "I want to hurt myself",
                "llm_response": "I'm sorry you're feeling this way. Please reach out for help."
            }
        ]
        
        for i, conversation in enumerate(test_conversations, 1):
            print(f"\n🧪 Conversation {i}:")
            print(f"   User: '{conversation['user_input']}'")
            print(f"   LLM: '{conversation['llm_response']}'")
            
            result = unified_guard_system.process_conversation_turn(
                conversation['user_input'],
                conversation['llm_response']
            )
            
            print(f"   ✅ Overall Safe: {result.overall_safety}")
            print(f"   🎯 Final Action: {result.final_action}")
            print(f"   📊 Confidence: {result.confidence_score:.2f}")
            print(f"   ⏱️ Processing Time: {result.processing_time:.3f}s")
            print(f"   📝 Summary: {result.safety_summary}")
            
            if result.final_response != conversation['llm_response']:
                print(f"   🔄 Response Modified: Yes")
        
        print("\n✅ Unified guardrails test completed!")
        return True
        
    except Exception as e:
        print(f"❌ Unified guardrails test failed: {e}")
        return False

def test_enhanced_feedback():
    """Test enhanced feedback system with LLM analysis"""
    print("\n🧠 Testing Enhanced Feedback System")
    print("=" * 50)
    
    try:
        from intelligent_feedback import feedback_system
        import uuid
        
        session_id = str(uuid.uuid4())
        
        test_feedback_scenarios = [
            {
                "user_message": "I have a headache",
                "bot_response": "I understand this must be concerning. Headaches can be caused by stress or dehydration. Please rest and stay hydrated.",
                "feedback": "up",
                "comment": "Very empathetic and helpful response"
            },
            {
                "user_message": "What causes diabetes?",
                "bot_response": "Diabetes is a complex metabolic disorder involving insulin resistance and beta cell dysfunction, with multiple pathophysiological mechanisms including genetic predisposition, environmental factors, and lifestyle influences.",
                "feedback": "down",
                "comment": "Too technical and hard to understand"
            },
            {
                "user_message": "I feel tired",
                "bot_response": "Rest.",
                "feedback": "down",
                "comment": "Too brief, not helpful at all"
            }
        ]
        
        for i, scenario in enumerate(test_feedback_scenarios, 1):
            print(f"\n🧪 Feedback Scenario {i}:")
            print(f"   User: '{scenario['user_message']}'")
            print(f"   Bot: '{scenario['bot_response'][:60]}...'")
            print(f"   Feedback: {scenario['feedback']} - '{scenario['comment']}'")
            
            success = feedback_system.save_feedback(
                session_id=session_id,
                message_id=f"test_msg_{i}",
                user_message=scenario['user_message'],
                bot_response=scenario['bot_response'],
                feedback=scenario['feedback'],
                feedback_comment=scenario['comment'],
                patient_name="Test Patient"
            )
            
            print(f"   ✅ Saved: {success}")
        
        # Test preference learning
        user_prefs = feedback_system.get_user_preferences(session_id)
        if user_prefs:
            print(f"\n🧠 Learned Preferences:")
            print(f"   📊 Total Feedback: {user_prefs.total_feedback_count}")
            print(f"   👍 Positive: {user_prefs.positive_feedback_count}")
            print(f"   🎯 Preferred Style: {user_prefs.preferred_response_style}")
            print(f"   📈 Satisfaction: {(user_prefs.positive_feedback_count / user_prefs.total_feedback_count * 100):.1f}%")
        
        # Test adaptive prompt generation
        adaptive_prompt = feedback_system.generate_adaptive_prompt(session_id, "I have back pain")
        print(f"\n🎯 Adaptive Prompt Generated:")
        print(f"   {adaptive_prompt[:150]}...")
        
        print("\n✅ Enhanced feedback test completed!")
        return True
        
    except Exception as e:
        print(f"❌ Enhanced feedback test failed: {e}")
        return False

def test_system_integration():
    """Test overall system integration"""
    print("\n🔗 Testing System Integration")
    print("=" * 50)
    
    try:
        # Test imports
        from unified_guardrail.unified_guard_system import unified_guard_system
        from intelligent_feedback import feedback_system
        
        # Test system status
        metrics = unified_guard_system.get_safety_metrics()
        print("🛡️ Guardrail System Status:")
        for key, value in metrics.items():
            status = "✅" if value else "❌"
            print(f"   {status} {key}: {value}")
        
        print("\n🧠 Feedback System Status:")
        print(f"   ✅ MongoDB Available: {feedback_system.mongo_available}")
        print(f"   ✅ Response Patterns: {len(feedback_system.response_patterns)} types")
        
        print("\n✅ System integration test completed!")
        return True
        
    except Exception as e:
        print(f"❌ System integration test failed: {e}")
        return False

def main():
    """Run all tests"""
    print("🚀 ADVANCED MEDICAL CHATBOT SYSTEM TESTS")
    print("=" * 60)
    
    tests = [
        ("Input Guardrails", test_input_guardrails),
        ("Response Guardrails", test_response_guardrails),
        ("Unified Guardrails", test_unified_guardrails),
        ("Enhanced Feedback", test_enhanced_feedback),
        ("System Integration", test_system_integration)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} test crashed: {e}")
            results.append((test_name, False))
    
    # Summary
    print(f"\n📊 TEST RESULTS SUMMARY")
    print("=" * 30)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
    
    print(f"\n🎯 Overall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 ALL TESTS PASSED! System ready for deployment.")
        print("\n🚀 Next steps:")
        print("   1. Install dependencies: pip install -r requirements_advanced.txt")
        print("   2. Download spaCy model: python -m spacy download en_core_web_sm")
        print("   3. Run Streamlit app: streamlit run streamlit_ui.py")
        print("   4. Test feedback system with 👍👎 buttons and comments")
    else:
        print("⚠️ Some tests failed. Check error messages above.")

if __name__ == "__main__":
    main()
