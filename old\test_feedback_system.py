#!/usr/bin/env python3
"""
Test script for the Intelligent Feedback System
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from intelligent_feedback import feedback_system
import uuid

def test_feedback_learning():
    """Test the feedback learning system"""
    print("🧪 Testing Intelligent Feedback System")
    print("=" * 50)
    
    # Create a test session
    session_id = str(uuid.uuid4())
    patient_name = "<PERSON>"
    
    print(f"📝 Test Session ID: {session_id}")
    print(f"👤 Test Patient: {patient_name}")
    
    # Test scenarios with different response types
    test_scenarios = [
        {
            "user_message": "I have a headache",
            "bot_response": "I understand this must be concerning for you. Headaches can be uncomfortable. Here's what might help, though please remember to consult your doctor: rest, hydration, and over-the-counter pain relief if appropriate.",
            "feedback": "up",
            "expected_type": "empathetic"
        },
        {
            "user_message": "What causes diabetes?",
            "bot_response": "Based on medical information, diabetes is caused by the body's inability to properly process glucose. Type 1 diabetes results from autoimmune destruction of insulin-producing cells, while Type 2 diabetes involves insulin resistance.",
            "feedback": "up", 
            "expected_type": "clinical"
        },
        {
            "user_message": "I feel tired all the time",
            "bot_response": "Fatigue can have many causes including sleep disorders, nutritional deficiencies, thyroid problems, depression, chronic conditions like diabetes or heart disease, medications, stress, or lifestyle factors. It's important to track when you feel most tired, what activities worsen it, your sleep patterns, diet, and any other symptoms. Consider keeping a fatigue diary and consult your healthcare provider for proper evaluation and testing.",
            "feedback": "down",  # User doesn't like detailed responses
            "expected_type": "detailed"
        },
        {
            "user_message": "Should I see a doctor?",
            "bot_response": "Yes, see a doctor.",
            "feedback": "down",  # Too brief
            "expected_type": "brief"
        },
        {
            "user_message": "I'm worried about my symptoms",
            "bot_response": "I can imagine how concerning this must be for you. It's completely natural to feel worried about symptoms. Please know that seeking medical advice is the right step.",
            "feedback": "up",  # User likes empathetic responses
            "expected_type": "empathetic"
        }
    ]
    
    print("\n🔄 Processing Test Scenarios...")
    
    for i, scenario in enumerate(test_scenarios, 1):
        print(f"\n📋 Scenario {i}:")
        print(f"   User: {scenario['user_message']}")
        print(f"   Bot: {scenario['bot_response'][:80]}...")
        print(f"   Feedback: {scenario['feedback']}")
        
        # Save feedback
        message_id = f"test_msg_{i}"
        success = feedback_system.save_feedback(
            session_id=session_id,
            message_id=message_id,
            user_message=scenario['user_message'],
            bot_response=scenario['bot_response'],
            feedback=scenario['feedback'],
            patient_name=patient_name
        )
        
        if success:
            print(f"   ✅ Feedback saved successfully")
        else:
            print(f"   ❌ Failed to save feedback")
    
    # Test preference learning
    print("\n🧠 Testing Preference Learning...")
    user_prefs = feedback_system.get_user_preferences(session_id)
    
    if user_prefs:
        print(f"✅ User Preferences Learned:")
        print(f"   - Patient Name: {user_prefs.patient_name}")
        print(f"   - Preferred Style: {user_prefs.preferred_response_style}")
        print(f"   - Total Feedback: {user_prefs.total_feedback_count}")
        print(f"   - Positive Feedback: {user_prefs.positive_feedback_count}")
        print(f"   - Satisfaction Rate: {(user_prefs.positive_feedback_count / user_prefs.total_feedback_count * 100):.1f}%")
    else:
        print("❌ No preferences learned")
    
    # Test adaptive prompt generation
    print("\n🎯 Testing Adaptive Prompt Generation...")
    test_message = "I have chest pain"
    adaptive_prompt = feedback_system.generate_adaptive_prompt(session_id, test_message)
    
    print(f"📝 Test Message: {test_message}")
    print(f"🤖 Adaptive Prompt Generated:")
    print(f"   {adaptive_prompt[:200]}...")
    
    # Test with new user (no preferences)
    print("\n🆕 Testing New User (No Preferences)...")
    new_session_id = str(uuid.uuid4())
    default_prompt = feedback_system.generate_adaptive_prompt(new_session_id, test_message)
    
    print(f"📝 New User Prompt:")
    print(f"   {default_prompt[:200]}...")
    
    print("\n✅ Feedback System Test Completed!")
    
    return user_prefs

def test_response_analysis():
    """Test response type analysis"""
    print("\n🔍 Testing Response Type Analysis...")
    print("=" * 40)
    
    test_responses = [
        ("I understand this must be concerning for you. I can imagine how you feel.", "empathetic"),
        ("Based on medical research, the symptoms indicate a possible viral infection.", "clinical"),
        ("Headaches can be caused by stress, dehydration, eye strain, sinus problems, tension, migraines, cluster headaches, medication overuse, sleep disorders, or underlying medical conditions. Prevention includes regular sleep, hydration, stress management, proper posture, regular meals, limiting screen time, and avoiding known triggers.", "detailed"),
        ("Take rest and see a doctor.", "brief")
    ]
    
    for response, expected_type in test_responses:
        analyzed_type = feedback_system._analyze_response_type(response)
        status = "✅" if analyzed_type == expected_type else "❌"
        print(f"{status} '{response[:50]}...' → {analyzed_type} (expected: {expected_type})")

def main():
    """Run all tests"""
    print("🚀 INTELLIGENT FEEDBACK SYSTEM TESTS")
    print("=" * 60)
    
    try:
        # Test feedback learning
        user_prefs = test_feedback_learning()
        
        # Test response analysis
        test_response_analysis()
        
        print(f"\n🎉 ALL TESTS COMPLETED!")
        print(f"📊 System Status: {'✅ Ready' if user_prefs else '⚠️ Needs Setup'}")
        
        if user_prefs:
            print(f"\n🧠 Learning Summary:")
            print(f"   - The system learned that the user prefers: {user_prefs.preferred_response_style} responses")
            print(f"   - Satisfaction rate: {(user_prefs.positive_feedback_count / user_prefs.total_feedback_count * 100):.1f}%")
            print(f"   - Total interactions: {user_prefs.total_feedback_count}")
        
        print(f"\n🚀 Ready to run: streamlit run streamlit_ui.py")
        print(f"💡 Use 👍👎 buttons to train the system!")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
