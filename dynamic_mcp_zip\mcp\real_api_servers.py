"""
Real API-based MCP Servers
These servers connect to actual external APIs instead of using LLM responses
"""

import asyncio
import logging
import json
import aiohttp
from typing import Dict, Any, List, Optional
from abc import ABC, abstractmethod

logger = logging.getLogger(__name__)

class RealAPIMCPServer(ABC):
    """Base class for real API-based MCP servers"""
    
    def __init__(self, name: str, api_key: str):
        self.name = name
        self.api_key = api_key
        self.is_active = False
        self.session = None
        
    async def initialize(self):
        """Initialize the server with HTTP session"""
        try:
            self.session = aiohttp.ClientSession()
            # Test the API key
            test_result = await self.test_api_connection()
            if test_result["valid"]:
                self.is_active = True
                logger.info(f"Real API MCP Server '{self.name}' initialized successfully")
                return True
            else:
                logger.error(f"API key validation failed for {self.name}: {test_result['error']}")
                return False
        except Exception as e:
            logger.error(f"Failed to initialize real API server '{self.name}': {e}")
            return False
    
    async def cleanup(self):
        """Cleanup resources"""
        if self.session:
            await self.session.close()
    
    @abstractmethod
    async def test_api_connection(self) -> Dict[str, Any]:
        """Test if the API connection is working"""
        pass
    
    @abstractmethod
    async def execute_tool(self, query: str, context: Dict[str, Any] = None) -> Dict[str, Any]:
        """Execute the tool with real API calls"""
        pass
    
    def get_tool_info(self) -> Dict[str, Any]:
        """Get information about this tool"""
        return {
            "name": self.name,
            "description": self.get_description(),
            "server_name": self.name,
            "active": self.is_active,
            "real_api": True
        }
    
    @abstractmethod
    def get_description(self) -> str:
        """Get tool description"""
        pass

class GoogleSearchMCPServer(RealAPIMCPServer):
    """Real Google Search API MCP Server"""
    
    def __init__(self, api_key: str, search_engine_id: str):
        super().__init__("google_search", api_key)
        self.search_engine_id = search_engine_id
        self.tool_name = "google_search"
        self.tool_description = "Real Google Search API for web search results"
    
    def get_description(self) -> str:
        return "Search the web using Google Custom Search API for real-time results"
    
    async def test_api_connection(self) -> Dict[str, Any]:
        """Test Google Search API"""
        try:
            url = "https://www.googleapis.com/customsearch/v1"
            params = {
                "key": self.api_key,
                "cx": self.search_engine_id,
                "q": "test",
                "num": 1
            }
            
            async with self.session.get(url, params=params) as response:
                if response.status == 200:
                    return {"valid": True, "message": "Google Search API is working"}
                else:
                    error_text = await response.text()
                    return {"valid": False, "error": f"API error {response.status}: {error_text}"}
                    
        except Exception as e:
            return {"valid": False, "error": str(e)}
    
    async def execute_tool(self, query: str, context: Dict[str, Any] = None) -> Dict[str, Any]:
        """Execute Google search"""
        try:
            if not self.is_active:
                return {"error": "Google Search API not initialized"}
            
            url = "https://www.googleapis.com/customsearch/v1"
            params = {
                "key": self.api_key,
                "cx": self.search_engine_id,
                "q": query,
                "num": 5
            }
            
            async with self.session.get(url, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    items = data.get("items", [])
                    
                    if not items:
                        return {
                            "success": True,
                            "tool_name": self.tool_name,
                            "response": f"🔍 **Google Search Results for '{query}':**\n\nNo results found.",
                            "server_name": self.name,
                            "real_data": True
                        }
                    
                    # Format results
                    formatted_results = f"🔍 **Google Search Results for '{query}':**\n\n"
                    
                    for i, item in enumerate(items[:3], 1):
                        title = item.get("title", "No title")
                        snippet = item.get("snippet", "No description")
                        link = item.get("link", "")
                        
                        formatted_results += f"**{i}. {title}**\n"
                        formatted_results += f"{snippet}\n"
                        formatted_results += f"🔗 {link}\n\n"
                    
                    formatted_results += f"*Found {len(items)} results via Google Search API*"
                    
                    return {
                        "success": True,
                        "tool_name": self.tool_name,
                        "response": formatted_results,
                        "server_name": self.name,
                        "real_data": True,
                        "results_count": len(items)
                    }
                else:
                    error_text = await response.text()
                    return {"error": f"Google Search API error {response.status}: {error_text}"}
                    
        except Exception as e:
            logger.error(f"Google Search execution failed: {e}")
            return {"error": str(e)}

class OpenWeatherMCPServer(RealAPIMCPServer):
    """Real OpenWeather API MCP Server"""
    
    def __init__(self, api_key: str):
        super().__init__("openweather", api_key)
        self.tool_name = "weather_search"
        self.tool_description = "Real weather data using OpenWeather API"
    
    def get_description(self) -> str:
        return "Get real-time weather information for any location using OpenWeather API"
    
    async def test_api_connection(self) -> Dict[str, Any]:
        """Test OpenWeather API"""
        try:
            url = "https://api.openweathermap.org/data/2.5/weather"
            params = {
                "q": "London",
                "appid": self.api_key,
                "units": "metric"
            }
            
            async with self.session.get(url, params=params) as response:
                if response.status == 200:
                    return {"valid": True, "message": "OpenWeather API is working"}
                else:
                    error_text = await response.text()
                    return {"valid": False, "error": f"API error {response.status}: {error_text}"}
                    
        except Exception as e:
            return {"valid": False, "error": str(e)}
    
    async def execute_tool(self, query: str, context: Dict[str, Any] = None) -> Dict[str, Any]:
        """Execute weather search"""
        try:
            if not self.is_active:
                return {"error": "OpenWeather API not initialized"}
            
            # Extract location from query
            location = self._extract_location(query)
            if not location:
                location = "London"  # Default location
            
            url = "https://api.openweathermap.org/data/2.5/weather"
            params = {
                "q": location,
                "appid": self.api_key,
                "units": "metric"
            }
            
            async with self.session.get(url, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    
                    # Format weather data
                    city = data["name"]
                    country = data["sys"]["country"]
                    temp = data["main"]["temp"]
                    feels_like = data["main"]["feels_like"]
                    humidity = data["main"]["humidity"]
                    description = data["weather"][0]["description"].title()
                    
                    formatted_result = f"🌤️ **Weather in {city}, {country}:**\n\n"
                    formatted_result += f"**Temperature:** {temp}°C (feels like {feels_like}°C)\n"
                    formatted_result += f"**Condition:** {description}\n"
                    formatted_result += f"**Humidity:** {humidity}%\n\n"
                    formatted_result += "*Real-time data from OpenWeather API*"
                    
                    return {
                        "success": True,
                        "tool_name": self.tool_name,
                        "response": formatted_result,
                        "server_name": self.name,
                        "real_data": True,
                        "location": f"{city}, {country}"
                    }
                else:
                    error_text = await response.text()
                    return {"error": f"OpenWeather API error {response.status}: {error_text}"}
                    
        except Exception as e:
            logger.error(f"Weather search execution failed: {e}")
            return {"error": str(e)}
    
    def _extract_location(self, query: str) -> str:
        """Extract location from query"""
        # Simple location extraction
        query_lower = query.lower()
        words = query_lower.split()
        
        # Look for common location indicators
        location_words = []
        skip_next = False
        
        for i, word in enumerate(words):
            if skip_next:
                skip_next = False
                continue
                
            if word in ["in", "for", "at", "weather"]:
                # Take the next word(s) as location
                if i + 1 < len(words):
                    location_words.append(words[i + 1])
                    if i + 2 < len(words) and words[i + 2] not in ["weather", "temperature", "forecast"]:
                        location_words.append(words[i + 2])
                break
        
        return " ".join(location_words) if location_words else ""

class NewsAPIMCPServer(RealAPIMCPServer):
    """Real News API MCP Server"""

    def __init__(self, api_key: str):
        super().__init__("newsapi", api_key)
        self.tool_name = "news_search"
        self.tool_description = "Real news articles using News API"

    def get_description(self) -> str:
        return "Get real-time news articles and headlines using News API"

    async def test_api_connection(self) -> Dict[str, Any]:
        """Test News API"""
        try:
            url = "https://newsapi.org/v2/top-headlines"
            headers = {"X-API-Key": self.api_key}
            params = {
                "country": "us",
                "pageSize": 1
            }

            async with self.session.get(url, headers=headers, params=params) as response:
                if response.status == 200:
                    return {"valid": True, "message": "News API is working"}
                else:
                    error_text = await response.text()
                    return {"valid": False, "error": f"API error {response.status}: {error_text}"}

        except Exception as e:
            return {"valid": False, "error": str(e)}

    async def execute_tool(self, query: str, context: Dict[str, Any] = None) -> Dict[str, Any]:
        """Execute news search"""
        try:
            if not self.is_active:
                return {"error": "News API not initialized"}

            # Determine if this is a search query or top headlines
            if any(word in query.lower() for word in ["search", "find", "about"]):
                # Search for specific news
                search_term = self._extract_search_term(query)
                url = "https://newsapi.org/v2/everything"
                params = {
                    "q": search_term,
                    "sortBy": "relevancy",
                    "pageSize": 5,
                    "language": "en"
                }
            else:
                # Get top headlines
                url = "https://newsapi.org/v2/top-headlines"
                params = {
                    "country": "us",
                    "pageSize": 5
                }

            headers = {"X-API-Key": self.api_key}

            async with self.session.get(url, headers=headers, params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    articles = data.get("articles", [])

                    if not articles:
                        return {
                            "success": True,
                            "tool_name": self.tool_name,
                            "response": f"📰 **News Results:**\n\nNo articles found for '{query}'.",
                            "server_name": self.name,
                            "real_data": True
                        }

                    # Format news results
                    formatted_results = f"📰 **Latest News:**\n\n"

                    for i, article in enumerate(articles[:3], 1):
                        title = article.get("title", "No title")
                        description = article.get("description", "No description")
                        url_link = article.get("url", "")
                        source = article.get("source", {}).get("name", "Unknown")

                        formatted_results += f"**{i}. {title}**\n"
                        formatted_results += f"*Source: {source}*\n"
                        formatted_results += f"{description}\n"
                        if url_link:
                            formatted_results += f"🔗 {url_link}\n"
                        formatted_results += "\n"

                    formatted_results += f"*Found {len(articles)} articles via News API*"

                    return {
                        "success": True,
                        "tool_name": self.tool_name,
                        "response": formatted_results,
                        "server_name": self.name,
                        "real_data": True,
                        "articles_count": len(articles)
                    }
                else:
                    error_text = await response.text()
                    return {"error": f"News API error {response.status}: {error_text}"}

        except Exception as e:
            logger.error(f"News search execution failed: {e}")
            return {"error": str(e)}

    def _extract_search_term(self, query: str) -> str:
        """Extract search term from query"""
        # Remove common words and extract the main search term
        stop_words = ["news", "about", "search", "find", "latest", "recent", "for"]
        words = [word for word in query.lower().split() if word not in stop_words]
        return " ".join(words) if words else "technology"
