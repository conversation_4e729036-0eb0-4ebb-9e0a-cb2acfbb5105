#!/usr/bin/env python3
"""
Unified Guardrail System for Medical Chatbot
Orchestrates input and response guardrails with intelligent decision making
"""

import sys
import os
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from datetime import datetime

# Add parent directory to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from input_guardrail.advanced_input_guard import advanced_input_guard, InputGuardResult
from response_guardrail.advanced_response_guard import advanced_response_guard, ResponseGuardResult

@dataclass
class UnifiedGuardResult:
    """Unified result from both input and response guardrails"""
    input_result: Optional[InputGuardResult]
    response_result: Optional[ResponseGuardResult]
    overall_safety: bool
    final_action: str  # "allow", "sanitize", "enhance", "block", "escalate"
    final_response: str
    safety_summary: str
    confidence_score: float
    processing_time: float

class UnifiedGuardSystem:
    """Unified guardrail system orchestrating input and response validation"""
    
    def __init__(self):
        self.input_guard = advanced_input_guard
        self.response_guard = advanced_response_guard
        
        # Emergency response templates
        self.emergency_responses = {
            "medical_emergency": (
                "🚨 **MEDICAL EMERGENCY DETECTED** 🚨\n\n"
                "Based on your message, this may require immediate medical attention.\n\n"
                "**PLEASE TAKE IMMEDIATE ACTION:**\n"
                "• Call emergency services NOW (911 in US, 999 in UK, 112 in EU)\n"
                "• Go to your nearest emergency room\n"
                "• If unable to travel, ask someone to call an ambulance\n\n"
                "**DO NOT DELAY** - Time is critical in medical emergencies.\n\n"
                "This system cannot provide emergency medical care. "
                "Professional medical intervention is required immediately."
            ),
            "high_risk_content": (
                "I'm concerned about your message and want to help. If you're having thoughts of "
                "self-harm, please contact a mental health professional immediately or call a crisis hotline. "
                "In the US: 988 Suicide & Crisis Lifeline. Your safety and wellbeing are important."
            ),
            "toxic_content": (
                "I'm here to provide helpful medical information in a respectful environment. "
                "Let's keep our conversation focused on your health concerns so I can assist you better."
            )
        }
        
        print("✅ Unified Guardrail System initialized")
    
    def process_conversation_turn(self, user_input: str, llm_response: str, 
                                user_context: Dict[str, Any] = None) -> UnifiedGuardResult:
        """Process a complete conversation turn with unified guardrails"""
        start_time = datetime.now()
        
        # Initialize result
        result = UnifiedGuardResult(
            input_result=None,
            response_result=None,
            overall_safety=True,
            final_action="allow",
            final_response=llm_response,
            safety_summary="",
            confidence_score=0.0,
            processing_time=0.0
        )
        
        try:
            # Step 1: Input Guardrails
            input_result = self.input_guard.analyze_input(user_input, user_context)
            result.input_result = input_result
            
            # Handle critical input issues
            if input_result.medical_emergency:
                result.overall_safety = False
                result.final_action = "escalate"
                result.final_response = self.emergency_responses["medical_emergency"]
                result.safety_summary = "Medical emergency detected - immediate escalation"
                result.confidence_score = input_result.confidence_score
                return self._finalize_result(result, start_time)
            
            if input_result.action_required == "block":
                result.overall_safety = False
                result.final_action = "block"
                if input_result.toxicity_score > 0.7:
                    result.final_response = self.emergency_responses["toxic_content"]
                else:
                    result.final_response = self.emergency_responses["high_risk_content"]
                result.safety_summary = f"Input blocked: {', '.join(input_result.detected_issues)}"
                result.confidence_score = input_result.confidence_score
                return self._finalize_result(result, start_time)
            
            # Step 2: Response Guardrails (if input passed)
            response_result = self.response_guard.validate_response(llm_response, user_context)
            result.response_result = response_result
            
            # Handle response issues
            if not response_result.is_compliant:
                if response_result.inappropriate_advice or response_result.toxicity_score > 0.5:
                    result.overall_safety = False
                    result.final_action = "block"
                    result.final_response = self.response_guard.generate_safe_fallback_response(user_input)
                    result.safety_summary = f"Response blocked: {', '.join(response_result.detected_issues)}"
                else:
                    result.final_action = "enhance"
                    result.final_response = response_result.enhanced_response
                    result.safety_summary = f"Response enhanced: {', '.join(response_result.detected_issues)}"
            else:
                # Response is compliant, check if enhancement is beneficial
                if response_result.enhanced_response != llm_response:
                    result.final_action = "enhance"
                    result.final_response = response_result.enhanced_response
                    result.safety_summary = "Response enhanced with disclaimers and safety measures"
                else:
                    result.final_action = "allow"
                    result.final_response = llm_response
                    result.safety_summary = "Content approved - no issues detected"
            
            # Step 3: Input sanitization if needed
            if input_result.action_required == "sanitize" and result.final_action in ["allow", "enhance"]:
                # Note: The sanitized input would be used for LLM processing
                # Here we just note it in the summary
                result.safety_summary += f" | Input sanitized: {', '.join(input_result.detected_issues)}"
            
            # Calculate overall confidence
            input_confidence = input_result.confidence_score
            response_confidence = response_result.confidence_score
            result.confidence_score = (input_confidence + response_confidence) / 2
            
        except Exception as e:
            # Fail-safe: if guardrails fail, block the content
            result.overall_safety = False
            result.final_action = "block"
            result.final_response = (
                "I apologize, but I'm unable to process your request at the moment due to a safety check. "
                "Please try rephrasing your question, and I'll be happy to help with your medical concerns."
            )
            result.safety_summary = f"Guardrail system error: {str(e)}"
            result.confidence_score = 0.5
            print(f"Guardrail system error: {e}")
        
        return self._finalize_result(result, start_time)
    
    def process_input_only(self, user_input: str, user_context: Dict[str, Any] = None) -> Tuple[bool, str, str]:
        """Process input only (for pre-LLM filtering)"""
        start_time = datetime.now()
        
        input_result = self.input_guard.analyze_input(user_input, user_context)
        
        # Return: (should_proceed, sanitized_input, safety_message)
        if input_result.medical_emergency:
            return False, user_input, self.emergency_responses["medical_emergency"]
        elif input_result.action_required == "block":
            if input_result.toxicity_score > 0.7:
                safety_msg = self.emergency_responses["toxic_content"]
            else:
                safety_msg = self.emergency_responses["high_risk_content"]
            return False, user_input, safety_msg
        elif input_result.action_required == "sanitize":
            return True, input_result.sanitized_input, "Input sanitized for safety"
        else:
            return True, user_input, "Input approved"
    
    def process_response_only(self, response: str, user_context: Dict[str, Any] = None) -> Tuple[bool, str, str]:
        """Process response only (for post-LLM validation)"""
        response_result = self.response_guard.validate_response(response, user_context)
        
        # Return: (is_safe, final_response, enhancement_summary)
        if not response_result.is_compliant:
            if response_result.inappropriate_advice or response_result.toxicity_score > 0.5:
                safe_response = self.response_guard.generate_safe_fallback_response("")
                return False, safe_response, f"Response blocked: {', '.join(response_result.detected_issues)}"
            else:
                return True, response_result.enhanced_response, f"Response enhanced: {', '.join(response_result.detected_issues)}"
        else:
            return True, response_result.enhanced_response, "Response approved"
    
    def _finalize_result(self, result: UnifiedGuardResult, start_time: datetime) -> UnifiedGuardResult:
        """Finalize the result with processing time"""
        end_time = datetime.now()
        result.processing_time = (end_time - start_time).total_seconds()
        return result
    
    def get_safety_metrics(self) -> Dict[str, Any]:
        """Get safety metrics from the guardrail system"""
        return {
            "input_guard_available": self.input_guard is not None,
            "response_guard_available": self.response_guard is not None,
            "presidio_available": hasattr(self.input_guard, 'analyzer_engine') and self.input_guard.analyzer_engine is not None,
            "detoxify_available": hasattr(self.input_guard, 'detoxify_model') and self.input_guard.detoxify_model is not None,
            "spacy_available": hasattr(self.response_guard, 'nlp') and self.response_guard.nlp is not None,
            "system_status": "fully_operational" if all([
                self.input_guard, self.response_guard
            ]) else "limited_functionality"
        }
    
    def generate_safety_report(self, result: UnifiedGuardResult) -> str:
        """Generate a detailed safety report"""
        report = f"""
🛡️ **UNIFIED GUARDRAIL SAFETY REPORT**
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

**Overall Assessment:** {'✅ SAFE' if result.overall_safety else '⚠️ UNSAFE'}
**Final Action:** {result.final_action.upper()}
**Confidence Score:** {result.confidence_score:.2f}
**Processing Time:** {result.processing_time:.3f}s

**Input Analysis:**"""
        
        if result.input_result:
            ir = result.input_result
            report += f"""
• Risk Level: {ir.risk_level.upper()}
• Medical Emergency: {'YES' if ir.medical_emergency else 'NO'}
• Toxicity Score: {ir.toxicity_score:.2f}
• Issues Detected: {', '.join(ir.detected_issues) if ir.detected_issues else 'None'}
• PII Detected: {', '.join(ir.pii_detected) if ir.pii_detected else 'None'}"""
        
        report += "\n\n**Response Analysis:**"
        
        if result.response_result:
            rr = result.response_result
            report += f"""
• Compliance: {'PASS' if rr.is_compliant else 'FAIL'}
• Medical Compliance: {'PASS' if rr.medical_compliance else 'FAIL'}
• Toxicity Score: {rr.toxicity_score:.2f}
• Inappropriate Advice: {'YES' if rr.inappropriate_advice else 'NO'}
• Issues Detected: {', '.join(rr.detected_issues) if rr.detected_issues else 'None'}
• Disclaimers Added: {', '.join(rr.disclaimers_added) if rr.disclaimers_added else 'None'}"""
        
        report += f"\n\n**Summary:** {result.safety_summary}"
        
        return report

# Global unified guardrail system
unified_guard_system = UnifiedGuardSystem()
